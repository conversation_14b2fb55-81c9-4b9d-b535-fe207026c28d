{"__schema": {"queryType": {"name": "Query", "kind": "OBJECT"}, "mutationType": {"name": "Mutation", "kind": "OBJECT"}, "subscriptionType": null, "types": [{"kind": "OBJECT", "name": "AppPaginator", "description": null, "isOneOf": null, "fields": [{"name": "current_page", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "has_more_pages", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "last_page", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "total", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "BaptismaRecord", "description": null, "isOneOf": null, "fields": [{"name": "age", "description": "eg: 1 month, 2 years", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "baptisma_chan_ni", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "baptisma_chan_ni_remarks", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "chantirtu", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "document", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Document", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "hming", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "khua", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "nu_hming", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "pa_hming", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "pian_ni", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "pian_ni_remarks", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "register_book_number", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "registration_no", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "BaptismaRecordFilterInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "baptisma_chan_ni", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "chantirtu", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "khua", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "nu_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pa_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pian_ni", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "registration_no", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "BaptismaRecordInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "age", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "baptisma_chan_ni", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "baptisma_chan_ni_remarks", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "chantirtu", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hming", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "khua", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "nu_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pa_hming", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pian_ni", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pian_ni_remarks", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "register_book_number", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "registration_no", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "BaptismaRecordUpdateInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "age", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "baptisma_chan_ni", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "baptisma_chan_ni_remarks", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "chantirtu", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "khua", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "nu_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pa_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pian_ni", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "pian_ni_remarks", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "register_book_number", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "registration_no", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Boolean", "description": "The `Boolean` scalar type represents `true` or `false`.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Category", "description": null, "isOneOf": null, "fields": [{"name": "children", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Category", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "is_classified", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "is_leaf", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "parent", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "Category", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "parent_id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "subCategory", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Category", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "CategoryPaginator", "description": null, "isOneOf": null, "fields": [{"name": "data", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Category", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paginator_info", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AppPaginator", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Date", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "DateTime", "description": "A datetime string with format `Y-m-d H:i:s`, e.g. `2018-05-23 13:43:32`.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "DocFile", "description": null, "isOneOf": null, "fields": [{"name": "doc_id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "document", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Document", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "file_size", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "file_type", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "path", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Document", "description": null, "isOneOf": null, "fields": [{"name": "added_date", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "body", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "category", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "Category", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "category_id", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "extra_record", "description": null, "args": [], "type": {"kind": "UNION", "name": "ExtraRecordMorph", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "files", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DocFile", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "is_classified", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "tags", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "DocumentByCategoryPaginator", "description": null, "isOneOf": null, "fields": [{"name": "data", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Document", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paginator_info", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AppPaginator", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "DocumentCountData", "description": null, "isOneOf": null, "fields": [{"name": "child_category", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "document_id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "document_title", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "parent_category", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "view_count", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "DocumentInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "added_date", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "body", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "category_id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "files", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Upload", "ofType": null}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "is_classified", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "tags", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "DocumentSpotlightCount", "description": null, "isOneOf": null, "fields": [{"name": "document_count", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "spotlight_count", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "DocumentUpdateInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "added_date", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "body", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "category_id", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "files", "description": null, "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Upload", "ofType": null}}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "is_classified", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "tags", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "DocumentViewCountResponse", "description": null, "isOneOf": null, "fields": [{"name": "data", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DocumentCountData", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paginator_info", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AppPaginator", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "UNION", "name": "ExtraRecordMorph", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": [{"kind": "OBJECT", "name": "BaptismaRecord", "ofType": null}, {"kind": "OBJECT", "name": "InneihRecord", "ofType": null}]}, {"kind": "OBJECT", "name": "GetDocumentPaginator", "description": null, "isOneOf": null, "fields": [{"name": "data", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Document", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paginator_info", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AppPaginator", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "ID", "description": "The `ID` scalar type represents a unique identifier, often used to refetch an object or as key for a cache. The ID type appears in a JSON response as a String; however, it is not intended to be human-readable. When expected as an input type, any string (such as `\"4\"`) or integer (such as `4`) input value will be accepted as an ID.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "InneihRecord", "description": null, "isOneOf": null, "fields": [{"name": "document", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Document", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "hmeichhe_hming", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "hmeichhe_khua", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "hmeichhe_pa_hming", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "hmun", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inneih_ni", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "inneihtirtu", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_hming", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_khua", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_pa_hming", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "register_book_number", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "registration_no", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "InneihRecordFilterInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "hmeichhe_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hmeichhe_pa_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hmun", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "inneih_ni", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "inneihtirtu", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_pa_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "registration_no", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "InneihRecordInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "hmeichhe_hming", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hmeichhe_khua", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hmeichhe_pa_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hmun", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "inneih_ni", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "inneihtirtu", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_hming", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_khua", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_pa_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "register_book_number", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "registration_no", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "InneihRecordUpdateInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "hmeichhe_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hmeichhe_khua", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hmeichhe_pa_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "hmun", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "inneih_ni", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "inneihtirtu", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_khua", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "mipa_pa_hming", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "register_book_number", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "registration_no", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "Int", "description": "The `Int` scalar type represents non-fractional signed whole numeric values. Int can represent values between -(2^31) and 2^31 - 1.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "LoginResponse", "description": null, "isOneOf": null, "fields": [{"name": "exp_date", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "token", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "user", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "User", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Mutation", "description": null, "isOneOf": null, "fields": [{"name": "addCategory", "description": null, "args": [{"name": "is_classified", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "parent_id", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "addRecord", "description": null, "args": [{"name": "baptisma_record", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "BaptismaRecordInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "document", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "DocumentInput", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "inneih_record", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "InneihRecordInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "addSpotlight", "description": null, "args": [{"name": "body", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "image", "description": null, "type": {"kind": "SCALAR", "name": "Upload", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deleteCategory", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Category", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deleteDocument", "description": null, "args": [{"name": "document_id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deleteDocumentFileById", "description": null, "args": [{"name": "document_file_id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deleteSpotlight", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Spotlight", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "generateDownloadLink", "description": null, "args": [{"name": "doc_file_id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "login", "description": null, "args": [{"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "password", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "LoginResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "logout", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateCategory", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "is_classified", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "parent_id", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateCategoryClassifiedStatus", "description": null, "args": [{"name": "category_id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "is_classified", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateRecord", "description": null, "args": [{"name": "baptisma_record", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "BaptismaRecordUpdateInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "document", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "INPUT_OBJECT", "name": "DocumentUpdateInput", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "inneih_record", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "InneihRecordUpdateInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updateSpotlight", "description": null, "args": [{"name": "body", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "image", "description": null, "type": {"kind": "SCALAR", "name": "Upload", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "upsertSetting", "description": null, "args": [{"name": "key", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON>ey", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "OrderByClause", "description": "Allows ordering a list of records.", "isOneOf": false, "fields": null, "inputFields": [{"name": "column", "description": "The column that is used for ordering.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "order", "description": "The direction that is used for ordering.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "SortOrder", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "OrderByRelationAggregateFunction", "description": "Aggregate functions when ordering by a relation without specifying a column.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "COUNT", "description": "Amount of items.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "OrderByRelationWithColumnAggregateFunction", "description": "Aggregate functions when ordering by a relation that may specify a column.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "AVG", "description": "Average.", "isDeprecated": false, "deprecationReason": null}, {"name": "COUNT", "description": "Amount of items.", "isDeprecated": false, "deprecationReason": null}, {"name": "MAX", "description": "Maximum.", "isDeprecated": false, "deprecationReason": null}, {"name": "MIN", "description": "Minimum.", "isDeprecated": false, "deprecationReason": null}, {"name": "SUM", "description": "Sum.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "INPUT_OBJECT", "name": "OtherRecordInput", "description": null, "isOneOf": false, "fields": null, "inputFields": [{"name": "added_date", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "body", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "tag", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "PaginatorInfo", "description": "Information about pagination using a fully featured paginator.", "isOneOf": null, "fields": [{"name": "count", "description": "Number of items in the current page.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "currentPage", "description": "Index of the current page.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "firstItem", "description": "Index of the first item in the current page.", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "hasMorePages", "description": "Are there more pages after this one?", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastItem", "description": "Index of the last item in the current page.", "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "lastPage", "description": "Index of the last available page.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "perPage", "description": "Number of items per page.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "total", "description": "Number of total available items.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Query", "description": "Indicates what fields are available at the top level of a query operation.", "isOneOf": null, "fields": [{"name": "getCategories", "description": null, "args": [{"name": "keyword", "description": null, "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "only_leaf", "description": "returns only the leaf node", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "unnested", "description": "returns nested category with children", "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Category", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "getCategoryById", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "OBJECT", "name": "Category", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "getDocumentById", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "OBJECT", "name": "Document", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "getDocumentSpotlightCount", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DocumentSpotlightCount", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "getDocumentViewCount", "description": null, "args": [{"name": "category_id", "description": null, "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "page", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "time_span", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TimeSpan", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DocumentViewCountResponse", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "getDocuments", "description": null, "args": [{"name": "added_date", "description": null, "type": {"kind": "SCALAR", "name": "Date", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "baptisma_filter", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "BaptismaRecordFilterInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "inneih_filter", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "InneihRecordFilterInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "others_filter", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "OtherRecordInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "page", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "GetDocumentPaginator", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "getDocumentsByCategoryId", "description": null, "args": [{"name": "baptisma_filter", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "BaptismaRecordFilterInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "category_id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "inneih_filter", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "InneihRecordFilterInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "others_filter", "description": null, "type": {"kind": "INPUT_OBJECT", "name": "OtherRecordInput", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "page", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "DocumentByCategoryPaginator", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "getSetting", "description": null, "args": [{"name": "name", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON>ey", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Settings", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "getSpotlightById", "description": null, "args": [{"name": "id", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "OBJECT", "name": "Spotlight", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "getSpotlightViewCount", "description": null, "args": [{"name": "first", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "page", "description": null, "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "time_span", "description": null, "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "TimeSpan", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "OBJECT", "name": "SpotlightViewCountResponse", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "getSpotlights", "description": null, "args": [{"name": "first", "description": "Limits number of fetched items.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}, {"name": "page", "description": "The offset from which items are returned.", "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "SpotlightPaginator", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "Settings", "description": null, "isOneOf": null, "fields": [{"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "value", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "<PERSON><PERSON><PERSON><PERSON>ey", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "LINK_LIFETIME", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "SortOrder", "description": "Directions for ordering a list of records.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ASC", "description": "Sort records in ascending order.", "isDeprecated": false, "deprecationReason": null}, {"name": "DESC", "description": "Sort records in descending order.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "Spotlight", "description": null, "isOneOf": null, "fields": [{"name": "body", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "file_path", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "file_size", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Int", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "file_type", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "SpotlightPaginator", "description": "A paginated list of Spotlight items.", "isOneOf": null, "fields": [{"name": "data", "description": "A list of Spotlight items.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "Spotlight", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paginatorInfo", "description": "Pagination information about the list of items.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "PaginatorInfo", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "SpotlightViewCount", "description": null, "isOneOf": null, "fields": [{"name": "body", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "count", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Int", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "ID", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "title", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "SpotlightViewCountResponse", "description": null, "isOneOf": null, "fields": [{"name": "data", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "SpotlightViewCount", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "paginator_info", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "AppPaginator", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "SCALAR", "name": "String", "description": "The `String` scalar type represents textual data, represented as UTF-8 character sequences. The String type is most often used by GraphQL to represent free-form human-readable text.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "TimeSpan", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ALL_TIME", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "CURRENT_YEAR", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PAST_MONTH", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PAST_WEEK", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "PAST_YEAR", "description": null, "isDeprecated": false, "deprecationReason": null}, {"name": "TODAY", "description": null, "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "ENUM", "name": "Trashed", "description": "Specify if you want to include or exclude trashed results from a query.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "ONLY", "description": "Only return trashed results.", "isDeprecated": false, "deprecationReason": null}, {"name": "WITH", "description": "Return both trashed and non-trashed results.", "isDeprecated": false, "deprecationReason": null}, {"name": "WITHOUT", "description": "Only return non-trashed results.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "SCALAR", "name": "Upload", "description": null, "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "User", "description": "Account of a person who uses this application.", "isOneOf": null, "fields": [{"name": "created_at", "description": "When the account was created.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "id", "description": "Unique primary key.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "ID", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "updated_at", "description": "When the account was last updated.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "DateTime", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Directive", "description": "A Directive provides a way to describe alternate runtime execution and type validation behavior in a GraphQL document.\n\nIn some cases, you need to provide options to alter GraphQL's execution behavior in ways field arguments will not suffice, such as conditionally including or skipping a field. Directives provide this by describing additional information to the executor.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isRepeatable", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "locations", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__DirectiveLocation", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__DirectiveLocation", "description": "A Directive can be adjacent to many parts of the GraphQL language, a __DirectiveLocation describes one such possible adjacencies.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "QUERY", "description": "Location adjacent to a query operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "MUTATION", "description": "Location adjacent to a mutation operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "SUBSCRIPTION", "description": "Location adjacent to a subscription operation.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD", "description": "Location adjacent to a field.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_DEFINITION", "description": "Location adjacent to a fragment definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FRAGMENT_SPREAD", "description": "Location adjacent to a fragment spread.", "isDeprecated": false, "deprecationReason": null}, {"name": "INLINE_FRAGMENT", "description": "Location adjacent to an inline fragment.", "isDeprecated": false, "deprecationReason": null}, {"name": "VARIABLE_DEFINITION", "description": "Location adjacent to a variable definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCHEMA", "description": "Location adjacent to a schema definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "SCALAR", "description": "Location adjacent to a scalar definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Location adjacent to an object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "FIELD_DEFINITION", "description": "Location adjacent to a field definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ARGUMENT_DEFINITION", "description": "Location adjacent to an argument definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Location adjacent to an interface definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Location adjacent to a union definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Location adjacent to an enum definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM_VALUE", "description": "Location adjacent to an enum value definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Location adjacent to an input object type definition.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_FIELD_DEFINITION", "description": "Location adjacent to an input object field definition.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "description": "One possible value for a given Enum. Enum values are unique values, not a placeholder for a string or numeric value. However an Enum value is returned in a JSON response as a string.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Field", "description": "Object and Interface types are described by a list of Fields, each of which has a name, potentially a list of arguments, and a return type.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "args", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__InputValue", "description": "Arguments provided to Fields or Directives and the input fields of an InputObject are represented as Input Values which describe their type and optionally a default value.", "isOneOf": null, "fields": [{"name": "name", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "String", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "type", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "defaultValue", "description": "A GraphQL-formatted string representing the default value for this input value.", "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isDeprecated", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "deprecationReason", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__<PERSON><PERSON><PERSON>", "description": "A GraphQL Schema defines the capabilities of a GraphQL server. It exposes all available types and directives on the server, as well as the entry points for query, mutation, and subscription operations.", "isOneOf": null, "fields": [{"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "types", "description": "A list of all types supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "queryType", "description": "The type that query operations will be rooted at.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "mutationType", "description": "If this server supports mutation, the type that mutation operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "subscriptionType", "description": "If this server support subscription, the type that subscription operations will be rooted at.", "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "directives", "description": "A list of all directives supported by this server.", "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Directive", "ofType": null}}}}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "OBJECT", "name": "__Type", "description": "The fundamental unit of any GraphQL Schema is the type. There are many kinds of types in GraphQL as represented by the `__TypeKind` enum.\n\nDepending on the kind of a type, certain fields describe information about that type. Scalar types provide no information beyond a name, description and optional `specifiedByURL`, while Enum types provide their values. Object and Interface types provide the fields they describe. Abstract types, Union and Interface, provide the Object types possible at runtime. List and NonNull types compose other types.", "isOneOf": null, "fields": [{"name": "kind", "description": null, "args": [], "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "ENUM", "name": "__TypeKind", "ofType": null}}, "isDeprecated": false, "deprecationReason": null}, {"name": "name", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "description", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "specifiedByURL", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "fields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Field", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "interfaces", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "possibleTypes", "description": null, "args": [], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__Type", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "enum<PERSON><PERSON><PERSON>", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__<PERSON>umV<PERSON><PERSON>", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "inputFields", "description": null, "args": [{"name": "includeDeprecated", "description": null, "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "defaultValue": "false", "isDeprecated": false, "deprecationReason": null}], "type": {"kind": "LIST", "name": null, "ofType": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "OBJECT", "name": "__InputValue", "ofType": null}}}, "isDeprecated": false, "deprecationReason": null}, {"name": "ofType", "description": null, "args": [], "type": {"kind": "OBJECT", "name": "__Type", "ofType": null}, "isDeprecated": false, "deprecationReason": null}, {"name": "isOneOf", "description": null, "args": [], "type": {"kind": "SCALAR", "name": "Boolean", "ofType": null}, "isDeprecated": false, "deprecationReason": null}], "inputFields": null, "interfaces": [], "enumValues": null, "possibleTypes": null}, {"kind": "ENUM", "name": "__TypeKind", "description": "An enum describing what kind of type a given `__Type` is.", "isOneOf": null, "fields": null, "inputFields": null, "interfaces": null, "enumValues": [{"name": "SCALAR", "description": "Indicates this type is a scalar.", "isDeprecated": false, "deprecationReason": null}, {"name": "OBJECT", "description": "Indicates this type is an object. `fields` and `interfaces` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "INTERFACE", "description": "Indicates this type is an interface. `fields`, `interfaces`, and `possibleTypes` are valid fields.", "isDeprecated": false, "deprecationReason": null}, {"name": "UNION", "description": "Indicates this type is a union. `possibleTypes` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "ENUM", "description": "Indicates this type is an enum. `enumValues` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "INPUT_OBJECT", "description": "Indicates this type is an input object. `inputFields` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "LIST", "description": "Indicates this type is a list. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}, {"name": "NON_NULL", "description": "Indicates this type is a non-null. `ofType` is a valid field.", "isDeprecated": false, "deprecationReason": null}], "possibleTypes": null}], "directives": [{"name": "deprecated", "description": "Marks an element of a GraphQL schema as no longer supported.", "isRepeatable": false, "locations": ["ARGUMENT_DEFINITION", "ENUM_VALUE", "FIELD_DEFINITION", "INPUT_FIELD_DEFINITION"], "args": [{"name": "reason", "description": "Explains why this element was deprecated, usually also including a suggestion for how to access supported similar data. Formatted using the Markdown syntax, as specified by [CommonMark](https://commonmark.org/).", "type": {"kind": "SCALAR", "name": "String", "ofType": null}, "defaultValue": "\"No longer supported\"", "isDeprecated": false, "deprecationReason": null}]}, {"name": "include", "description": "Directs the executor to include this field or fragment only when the `if` argument is true.", "isRepeatable": false, "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Included when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}, {"name": "oneOf", "description": "Indicates that an Input Object is a OneOf Input Object (and thus requires exactly one of its fields be provided).", "isRepeatable": false, "locations": ["INPUT_OBJECT"], "args": []}, {"name": "skip", "description": "Directs the executor to skip this field or fragment when the `if` argument is true.", "isRepeatable": false, "locations": ["FIELD", "FRAGMENT_SPREAD", "INLINE_FRAGMENT"], "args": [{"name": "if", "description": "Skipped when true.", "type": {"kind": "NON_NULL", "name": null, "ofType": {"kind": "SCALAR", "name": "Boolean", "ofType": null}}, "defaultValue": null, "isDeprecated": false, "deprecationReason": null}]}]}}