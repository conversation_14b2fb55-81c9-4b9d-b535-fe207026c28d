import { graphql } from '~/gql'

export const GET_CATEGORIES = graphql(`
  query GetCategories(
    $keyword: String
    $only_leaf: Boolean
    $unnested: Boolean
  ) {
    getCategories(
      keyword: $keyword
      only_leaf: $only_leaf
      unnested: $unnested
    ) {
      id
      name
      parent {
        id
        name
        parent {
          id
          name
        }
      }
    }
  }
`)
