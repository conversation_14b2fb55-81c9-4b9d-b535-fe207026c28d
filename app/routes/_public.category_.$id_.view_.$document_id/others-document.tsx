import type { GetDocumentByIdQuery } from '~/gql/graphql'
import { format } from 'date-fns'
import { FileText, Image, Video } from 'lucide-react'
import { toast } from 'sonner'
import useGenerateDownloadLink from '~/hooks/use-generate-download-link'
import { parseGraphqlError } from '~/lib/parse-graphql-error'

interface Props {
  document: GetDocumentByIdQuery['getDocumentById']
  files: NonNullable<GetDocumentByIdQuery['getDocumentById']>['files']
}

export default function OthersDocument({ document, files }: Props) {
  const { generateDownloadLink } = useGenerateDownloadLink()

  const generateLink = (_fileId: string) => {
    generateDownloadLink.mutate(_fileId, {
      onSuccess: (data) => {
        window.open(data.generateDownloadLink, '_blank')
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  // Separate files by type (this is a simplified approach since we only have file_type)
  const imageFiles = files?.filter(file => file.file_type?.startsWith('image/')) || []
  const documentFiles = files?.filter(file => file.file_type === 'application/pdf' || file.file_type?.includes('document')) || []
  const videoFiles = files?.filter(file => file.file_type?.startsWith('video/')) || []

  if (generateDownloadLink.isPending) {
    return (
      <div className="mt-2 flex size-full grow flex-col">
        <div className={`
          mt-2 flex size-full w-full grow items-center justify-center
          rounded-t-md bg-white shadow
        `}
        >
          Downloading File
        </div>

      </div>
    )
  }

  return (
    <div>
      {/* Main document content */}
      <div className="mt-2 rounded-md bg-white px-8 py-12 shadow-sm">
        <h2 className="text-xl font-semibold">{document?.title}</h2>
        <div className="py-8 opacity-50">
          {document?.added_date && format(new Date(document.added_date), 'dd MMMM yyyy')}
        </div>
        {document?.body && (
          <div
            className="column-split"
            dangerouslySetInnerHTML={{ __html: document.body }}
          />
        )}
      </div>

      {/* Image files */}
      {imageFiles.length > 0 && (
        <div className="mt-2 rounded-md bg-white px-8 py-12 shadow-sm">
          <div className="grid grid-cols-12 gap-x-8 gap-y-8">
            {imageFiles.map(item => (
              <div key={item.id} className="col-span-3">
                <div className="flex flex-col items-center">
                  <Image
                    className={`
                      h-48 w-full cursor-pointer object-cover text-gray-400
                    `}
                    onClick={() => generateLink(item.id)}
                  />
                  <div className="mt-2 px-8 text-left">{generateDownloadLink.isPending ? 'Loading...' : item.path}</div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Document files */}
      {documentFiles.length > 0 && (
        <div className="mt-2 rounded-md bg-white py-12 shadow-sm">
          <div className="grid grid-cols-12 gap-x-2 gap-y-8">
            {documentFiles.map(item => (
              <div key={item.id} className="col-span-2">
                <button
                  type="button"
                  className="focus:outline-none"
                  onClick={() => generateLink(item.id)}
                >
                  <FileText className="text-footerBlack mx-12 text-9xl" />
                  <div className="px-12 text-center">{generateDownloadLink.isPending ? 'Loading...' : item.path}</div>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Video files */}
      {videoFiles.length > 0 && (
        <div className="mt-2 rounded-md bg-white px-8 py-12 shadow-sm">
          <div className="grid grid-cols-12 gap-x-2">
            {videoFiles.map((item, index) => (
              <div key={item.id} className="col-span-2">
                <button
                  type="button"
                  className={`
                    bg-footerBlack flex h-32 w-28 items-center justify-center
                    font-bold text-white
                    focus:outline-none
                  `}
                  onClick={() => generateLink(item.id)}
                >
                  <div className="flex flex-col items-center">
                    <Video className="mb-2" />
                    {`Video ${index + 1}`}
                  </div>
                </button>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
