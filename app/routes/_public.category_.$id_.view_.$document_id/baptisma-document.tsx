import type { FragmentType } from '~/gql'
import type { GetDocumentByIdQuery } from '~/gql/graphql'
import { FileText } from 'lucide-react'
import { toast } from 'sonner'
import { useFragment } from '~/gql'
import useGenerateDownloadLink from '~/hooks/use-generate-download-link'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { BAPTISMA_RECORD_FRAGMENT } from '../_public.category_.$id/graphql'

interface Props {
  record: FragmentType<typeof BAPTISMA_RECORD_FRAGMENT>
  files: NonNullable<GetDocumentByIdQuery['getDocumentById']>['files']
}

export default function BaptismaDocument({ record, files }: Props) {
  const baptismaRecord = useFragment(BAPTISMA_RECORD_FRAGMENT, record)
  const { generateDownloadLink } = useGenerateDownloadLink()

  const generateLink = (_fileId: string) => {
    generateDownloadLink.mutate(_fileId, {
      onSuccess: (data) => {
        window.open(data.generateDownloadLink, '_blank')
      },
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
    })
  }

  if (generateDownloadLink.isPending) {
    return (
      <div className="mt-2 flex size-full grow flex-col">
        <div className={`
          mt-2 flex size-full w-full grow items-center justify-center
          rounded-t-md bg-white shadow
        `}
        >
          Downloading File
        </div>

      </div>
    )
  }

  return (
    <div className="mt-2">
      {baptismaRecord && (
        <div className="mt-2 w-full rounded-t-md bg-white shadow">
          <div className="border-b-2 border-gold px-6 py-4 text-xl">
            {baptismaRecord.baptisma_registration_no}
          </div>
          <div className="grid grid-cols-12">
            <div className="col-span-3">
              <div className="grid grid-rows-12">
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Registration number
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Book register number
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Hming
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Age
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Pa hming
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Nu hming
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Pian ni
                </div>
                {/* <div className={` */}
                {/*   row-span-1 border-r-2 border-b-2 border-gold px-6 py-4 */}
                {/* `} */}
                {/* > */}
                {/*   Pian ni remarks */}
                {/* </div> */}
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Baptisma chan ni
                </div>
                {/* <div className={` */}
                {/*   row-span-1 border-r-2 border-b-2 border-gold px-6 py-4 */}
                {/* `} */}
                {/* > */}
                {/*   Baptisma chan ni remarks */}
                {/* </div> */}
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Khua
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  Chantirtu
                </div>
                <div className="row-span-5 h-full border-r-2 border-gold" />
              </div>
            </div>
            <div className="col-span-6">
              <div className="grid grid-rows-12">
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {baptismaRecord.baptisma_registration_no || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {baptismaRecord.register_book_number || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {baptismaRecord.hming || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {baptismaRecord.age || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {baptismaRecord.pa_hming || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {baptismaRecord.nu_hming || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {baptismaRecord.pian_ni || '\u00A0'}
                </div>
                {/* <div className={` */}
                {/*   row-span-1 border-r-2 border-b-2 border-gold px-6 py-4 */}
                {/* `} */}
                {/* > */}
                {/*   {baptismaRecord.pian_ni_remarks || '\u00A0'} */}
                {/* </div> */}
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {baptismaRecord.baptisma_chan_ni || '\u00A0'}
                </div>
                {/* <div className={` */}
                {/*   row-span-1 border-r-2 border-b-2 border-gold px-6 py-4 */}
                {/* `} */}
                {/* > */}
                {/*   {baptismaRecord.baptisma_chan_ni_remarks || '\u00A0'} */}
                {/* </div> */}
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {baptismaRecord.khua || '\u00A0'}
                </div>
                <div className={`
                  row-span-1 border-r-2 border-b-2 border-gold px-6 py-4
                `}
                >
                  {baptismaRecord.chantirtu || '\u00A0'}
                </div>
                <div className="row-span-5 h-full border-r-2 border-gold" />
              </div>
            </div>
            <div className="col-span-3">
              {files && files.length > 0 && (
                <div className="mt-2 px-4 py-4">
                  <div className="mb-4 border-b text-xl">File(s)</div>
                  <div className="grid grid-cols-2 gap-x-2 gap-y-4">
                    {files.map(item => (
                      <div key={item.id} className="col-span-2">
                        <button
                          type="button"
                          className={`
                            flex items-center gap-x-2
                            focus:outline-none
                          `}
                          onClick={() => generateLink(item.id)}
                        >
                          <FileText className="size-6 shrink-0" />
                          <div className="break-all">{item.path}</div>
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
