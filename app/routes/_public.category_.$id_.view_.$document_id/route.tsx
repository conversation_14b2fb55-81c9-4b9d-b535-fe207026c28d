import { useParams } from 'react-router'
import BaptismaDocument from './baptisma-document'
import BreadcrumbCard from './breadcrumb-card'
import InneihDocument from './inneih-document'
import OthersDocument from './others-document'
import useGetDocumentById from './use-get-document-by-id'

export default function CategoryDocumentById() {
  const { document_id } = useParams()
  const { data, isLoading, isError } = useGetDocumentById(document_id!)

  const record = data?.getDocumentById?.extra_record
  const files = data?.getDocumentById?.files || []

  if (isLoading) {
    return <div>Loading...</div>
  }

  if (isError) {
    return <div>Error loading document</div>
  }

  return (
    <>
      <BreadcrumbCard id={document_id!} />
      {record && record.__typename === 'BaptismaRecord' && (
        <BaptismaDocument record={record} files={files} />
      )}
      {record && record.__typename === 'InneihRecord' && (
        <InneihDocument record={record} files={files} />
      )}
      {!record && (
        <OthersDocument document={data?.getDocumentById} files={files} />
      )}
    </>
  )
}
