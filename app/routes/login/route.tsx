import { useNavigate } from 'react-router'
import { toast } from 'sonner'
import { useAppForm } from '~/hooks/form'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import useLogin from './hooks'
import { LoginForm } from './login-form'
import { loginSchema } from './schema'

export default function Index() {
  const navigate = useNavigate()
  const { login } = useLogin()

  const form = useAppForm({
    defaultValues: {
      name: '',
      password: '',
    },
    validators: {
      onSubmit: loginSchema,
    },
    onSubmit: async ({ value }) => {
      await login.mutateAsync(value, {
        onSuccess: () => {
          navigate('/admin/welcome')
        },
        onError: (error) => {
          toast.error(parseGraphqlError(error))
          console.error(error)
        },
      })
    },
  })

  return (
    <div className="flex h-screen w-full flex-col items-center justify-center">
      <h1 className="text-xl font-bold">Welcome</h1>
      <div>Please login to continue</div>
      <form
        onSubmit={(e) => {
          e.preventDefault()
          e.stopPropagation()
          form.handleSubmit()
        }}
      >
        <LoginForm form={form} />
      </form>
    </div>
  )
}
