import type { BaptismaRecordFilterInput, InneihRecordFilterInput, OtherRecordInput } from '~/gql/graphql'
import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, useQueryState } from 'nuqs'
import { useState } from 'react'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_DOCUMENTS_BY_CATEGORY_ID } from './graphql'

interface Props {
  id?: string
}

export default function useGetDocumentsByCategoryId({ id }: Props) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const [activeBaptismaFilter, setActiveBaptismaFilter] = useState<BaptismaRecordFilterInput | null>(null)
  const [activeInneihFilter, setActiveInneihFilter] = useState<InneihRecordFilterInput | null>(null)
  const [activeOthersFilter, setActiveOthersFilter] = useState<OtherRecordInput | null>(null)

  const handlePage = (page: number) => {
    setPage(page)
  }

  const searchBaptisma = (data: BaptismaRecordFilterInput | null) => {
    setPage(1) // Reset to first page on new search
    setActiveBaptismaFilter(data)
    // Clear other active filters
    setActiveInneihFilter(null)
    setActiveOthersFilter(null)
  }

  const searchInneih = (data: InneihRecordFilterInput | null) => {
    setPage(1) // Reset to first page on new search
    setActiveInneihFilter(data)
    // Clear other active filters
    setActiveBaptismaFilter(null)
    setActiveOthersFilter(null)
  }

  const searchOthers = (data: OtherRecordInput | null) => {
    setPage(1) // Reset to first page on new search
    setActiveOthersFilter(data)
    // Clear other active filters
    setActiveBaptismaFilter(null)
    setActiveInneihFilter(null)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-documents-by-category-id', id, page, activeBaptismaFilter, activeInneihFilter, activeOthersFilter],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_DOCUMENTS_BY_CATEGORY_ID,
        variables: {
          first: 20,
          page,
          category_id: id!,
          baptisma_filter: activeBaptismaFilter,
          inneih_filter: activeInneihFilter,
          others_filter: activeOthersFilter,
        },
      })
    },
    enabled: !!id,
  })

  const lastPage = data?.getDocumentsByCategoryId?.paginator_info?.last_page || 1

  return { data, isLoading, isError, page, handlePage, lastPage, searchBaptisma, searchInneih, searchOthers }
}
