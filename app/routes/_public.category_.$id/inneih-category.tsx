import type { InneihRecordFilterInput } from '~/gql/graphql'
import { SearchIcon, X } from 'lucide-react'
import { useState } from 'react'
import { Accordion, AccordionContent, AccordionItem } from '~/components/ui/accordion'
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from '~/components/ui/breadcrumb'
import { Button } from '~/components/ui/button'
import { Card, CardContent } from '~/components/ui/card'
import { useAppForm } from '~/hooks/form'

interface Props {
  searchInneih: (data: InneihRecordFilterInput | null) => void
  isLoading: boolean
}

export default function InneihCategory({ searchInneih, isLoading }: Props) {
  const [isSearchOpen, setIsSearchOpen] = useState(false)

  const form = useAppForm({
    defaultValues: {
      registration_no: '',
      mipa_hming: '',
      mipa_pa_hming: '',
      hmeichhe_hming: '',
      hmeichhe_pa_hming: '',
      inneih_ni: '',
      hmun: '',
      inneihtirtu: '',
    },
    onSubmit: async ({ value }) => {
      const isEmpty = Object.values(value).every(val => !val || val === '')
      searchInneih(isEmpty ? null : value)
    },
  })

  return (
    <Card className="my-4 bg-white">
      <CardContent>
        <div className="flex items-center justify-between">
          <div>
            <Breadcrumb className="pt-4 pb-4">
              <BreadcrumbList>
                <BreadcrumbItem>
                  <BreadcrumbLink href="/">Home</BreadcrumbLink>
                </BreadcrumbItem>
                <BreadcrumbSeparator>
                  /
                </BreadcrumbSeparator>
                <BreadcrumbItem>
                  Inneih Records
                </BreadcrumbItem>
              </BreadcrumbList>
            </Breadcrumb>
          </div>
          {isSearchOpen
            ? (
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => setIsSearchOpen(!isSearchOpen)}
                >
                  <X className="text-xl" />
                </Button>
              )
            : (
                <Button
                  size="icon"
                  variant="ghost"
                  onClick={() => setIsSearchOpen(!isSearchOpen)}
                >
                  <SearchIcon className="text-xl" />
                </Button>
              )}
        </div>
        <Accordion
          type="single"
          value={isSearchOpen ? 'search-form' : ''}
          className="border-none"
        >
          <AccordionItem value="search-form" className="border-none">
            <AccordionContent>
              <form
                onSubmit={(e) => {
                  e.preventDefault()
                  e.stopPropagation()
                  form.handleSubmit()
                }}
                className="grid grid-cols-4 gap-4 pt-4"
              >
                <form.AppField
                  name="registration_no"
                  children={field => <field.InputField label="Registration No" />}
                />
                <form.AppField
                  name="mipa_hming"
                  children={field => <field.InputField label="Mipa hming" />}
                />
                <form.AppField
                  name="mipa_pa_hming"
                  children={field => <field.InputField label="Mipa Pa hming" />}
                />
                <form.AppField
                  name="hmeichhe_hming"
                  children={field => <field.InputField label="Hmeichhe hming" />}
                />

                <form.AppField
                  name="hmeichhe_pa_hming"
                  children={field => <field.InputField label="Hmeichhe nu hming" />}
                />
                <form.AppField
                  name="inneih_ni"
                  children={field => <field.InputField label="Inneih ni" type="date" />}
                />
                <form.AppField
                  name="hmun"
                  children={field => <field.InputField label="Hmun" />}
                />
                <form.AppField
                  name="inneihtirtu"
                  children={field => <field.InputField label="Inneih tir tu" />}
                />
                <div className="col-span-1">
                  <Button type="submit" isLoading={isLoading}>
                    Search
                  </Button>
                </div>
              </form>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  )
}
