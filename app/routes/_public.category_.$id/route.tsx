import { useParams } from 'react-router'
import PagePagination from '~/components/common/page-pagination'
import { BAPTISMA_CATEGORY_ID, INNEIH_CATEGORY_ID } from '~/lib/constants'
import BaptismaCategory from './baptisma-category'
import BaptismaTable from './baptisma-table'
import InneihCategory from './inneih-category'
import InneihTable from './inneih-table'
import OthersCategory from './others-category'
import OthersTable from './others-table'
import useGetDocumentsByCategoryId from './use-get-documents-by-category-id'

export default function CategoryById() {
  const { id } = useParams()
  const { data, lastPage, handlePage, page, searchBaptisma, isLoading, searchInneih, searchOthers } = useGetDocumentsByCategoryId({ id })

  return (
    <>
      {id === BAPTISMA_CATEGORY_ID && (
        <>
          <BaptismaCategory searchBaptisma={searchBaptisma} isLoading={isLoading} />
          <BaptismaTable documents={data?.getDocumentsByCategoryId?.data || []} />
        </>
      )}
      {id === INNEIH_CATEGORY_ID && (
        <>
          <InneihCategory searchInneih={searchInneih} isLoading={isLoading} />
          <InneihTable documents={data?.getDocumentsByCategoryId?.data || []} />
        </>
      )}
      {id !== BAPTISMA_CATEGORY_ID && id !== INNEIH_CATEGORY_ID && (
        <>
          <OthersCategory id={id!} searchOthers={searchOthers} isLoading={isLoading} />
          <OthersTable documents={data?.getDocumentsByCategoryId?.data || []} />
        </>
      )}
      {lastPage > 1 && (
        <div className="mb-4">
          <PagePagination
            currentPage={page}
            handlePagePagination={handlePage}
            lastPage={lastPage}
          />
        </div>
      )}
    </>
  )
}
