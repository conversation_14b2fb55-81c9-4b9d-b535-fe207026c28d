import DeleteIcon from '~/components/icons/delete-icon'
import { Button } from '~/components/ui/button'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import useBoolean from '~/hooks/use-boolean'
import useDeleteFile from './use-delete-file'

interface Props {
  id: string
  onRemove: () => void
}

export default function DeleteFilePopover({ id, onRemove }: Props) {
  const { isOpen, toggle } = useBoolean()
  const { deleteFile } = useDeleteFile({ onRemove })

  return (
    <Popover modal={true} open={isOpen} onOpenChange={toggle}>
      <PopoverTrigger asChild>
        <Button variant="destructive" size="icon">
          <DeleteIcon />
        </Button>
      </PopoverTrigger>
      <PopoverContent>
        <div>
          Are you sure you want to delete this file?
        </div>
        <div className="flex justify-end gap-x-4 p-1">
          <Button variant="secondary" onClick={() => { toggle(false) }}>
            Cancel
          </Button>
          <Button
            isLoading={deleteFile.isPending}
            variant="destructive"
            onClick={() => {
              deleteFile.mutate(id)
              toggle(false)
            }}
          >
            Delete
          </Button>
        </div>

      </PopoverContent>
    </Popover>
  )
}
