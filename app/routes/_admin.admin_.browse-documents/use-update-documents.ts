import type { UpdateRecordType } from './schema'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { format } from 'date-fns'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UPDATE_RECORD } from './graphql'

export default function useUpdateDocuments() {
  const queryClient = useQueryClient()
  const updateBaptismaRecord = useMutation({
    mutationFn: async (data: UpdateRecordType) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_RECORD,
        variables: {
          baptisma_record: {
            ...data.baptisma_record,
            id: data.baptisma_record!.id,
            pian_ni: data.baptisma_record?.pian_ni ? format(new Date(data.baptisma_record.pian_ni), 'yyyy-MM-dd') : undefined,
            baptisma_chan_ni: data.baptisma_record?.baptisma_chan_ni ? format(new Date(data.baptisma_record.baptisma_chan_ni), 'yyyy-MM-dd') : undefined,
          },
          document: {
            ...data.document,
            added_date: data.document.added_date ? format(new Date(data.document.added_date), 'yyyy-MM-dd') : undefined,
          },
        },
      })
    },
    onSuccess: () => {
      toast.success('Record updated')
      queryClient.invalidateQueries({
        queryKey: ['get-baptisma'],
      })
    },
    onError: (err) => {
      toast.error(parseGraphqlError(err))
    },
  })

  const updateInneihRecord = useMutation({
    mutationFn: async (data: UpdateRecordType) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_RECORD,
        variables: {
          inneih_record: {
            ...data.inneih_record,
            id: data.inneih_record!.id,
            inneih_ni: data.inneih_record?.inneih_ni ? format(new Date(data.inneih_record.inneih_ni), 'yyyy-MM-dd') : undefined,
          },
          document: {
            ...data.document,
            added_date: data.document.added_date ? format(new Date(data.document.added_date), 'yyyy-MM-dd') : undefined,
          },
        },
      })
    },
    onError: (err) => {
      toast.error(parseGraphqlError(err))
    },
    onSuccess: () => {
      toast.success('Record updated')
      queryClient.invalidateQueries({
        queryKey: ['get-inneih'],
      })
    },
  })

  const updateOthersRecord = useMutation({
    mutationFn: async (data: UpdateRecordType) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_RECORD,
        variables: {
          document: {
            ...data.document,
            added_date: data.document.added_date ? format(new Date(data.document.added_date), 'yyyy-MM-dd') : undefined,
          },
        },
      })
    },
    onSuccess: () => {
      toast.success('Record updated')
      queryClient.invalidateQueries({
        queryKey: ['get-others'],
      })
    },
    onError: (err) => {
      toast.error(parseGraphqlError(err))
    },
  })

  return { updateBaptismaRecord, updateInneihRecord, updateOthersRecord }
}
