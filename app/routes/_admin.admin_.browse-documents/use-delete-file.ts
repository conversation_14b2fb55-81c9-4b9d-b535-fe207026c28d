import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { DELETE_FILE } from './graphql'

interface Props {
  onRemove: () => void
}

export default function useDeleteFile({ onRemove }: Props) {
  const queryClient = useQueryClient()

  const deleteFile = useMutation({
    mutationFn: async (id: string) => {
      const client = await graphqlClient()
      return client.request({
        document: DELETE_FILE,
        variables: {
          id,
        },
      })
    },
    onSuccess: () => {
      toast.success('File deleted')
      onRemove()
      queryClient.invalidateQueries({
        queryKey: ['get-baptisma'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-inneih'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-others'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { deleteFile }
}
