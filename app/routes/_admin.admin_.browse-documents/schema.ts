import z from 'zod'

export const updateInneihRecord = z.object({
  id: z.string(),
  registration_no: z.string().optional(),
  mipa_hming: z.string().optional(),
  mipa_pa_hming: z.string().optional(),
  mipa_khua: z.string().optional(),
  hmeichhe_hming: z.string().optional(),
  hmeichhe_pa_hming: z.string().optional(),
  hmeichhe_khua: z.string().optional(),
  hmun: z.string().optional(),
  inneih_ni: z.string().optional(),
  inneihtirtu: z.string().optional(),
  register_book_number: z.string().optional(),
})

export const updateBaptismaRecord = z.object({
  id: z.string(),
  registration_no: z.string().optional(),
  hming: z.string().optional(),
  pa_hming: z.string().optional(),
  nu_hming: z.string().optional(),
  khua: z.string().optional(),
  pian_ni: z.string().optional(),
  pian_ni_remarks: z.string().optional(),
  baptisma_chan_ni: z.string().optional(),
  baptisma_chan_ni_remarks: z.string().optional(),
  chantirtu: z.string().optional(),
  age: z.string().optional(),
  register_book_number: z.string().optional(),
})

export const updateDocument = z.object({
  id: z.string(),
  title: z.string().optional(),
  body: z.string().optional(),
  tags: z.string().optional(),
  is_classified: z.boolean().optional(),
  category_id: z.string().optional(),
  added_date: z.string().optional(),
  files: z.array(z.instanceof(File)).optional(),
})

export const updateRecordSchema = z.object({
  inneih_record: updateInneihRecord.optional(),
  baptisma_record: updateBaptismaRecord.optional(),
  document: updateDocument,
})

export type UpdateRecordType = z.infer<typeof updateRecordSchema>
// export type UpdateInneihRecordType = z.infer<typeof updateInneihRecord>
// export type UpdateBaptismaRecordType = z.infer<typeof updateBaptismaRecord>
