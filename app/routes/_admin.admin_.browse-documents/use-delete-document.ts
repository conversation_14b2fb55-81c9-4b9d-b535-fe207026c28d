import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { DELETE_DOCUMENT } from './graphql'

export default function useDeleteDocument() {
  const queryClient = useQueryClient()

  const deleteDocument = useMutation({
    mutationFn: async (document_id: string) => {
      const client = await graphqlClient()
      return client.request({
        document: DELETE_DOCUMENT,
        variables: {
          document_id,
        },
      })
    },
    onSuccess: async () => {
      queryClient.invalidateQueries({
        queryKey: ['get-baptisma'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-inneih'],
      })
      queryClient.invalidateQueries({
        queryKey: ['get-others'],
      })
      toast.success('Document deleted')
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { deleteDocument }
}
