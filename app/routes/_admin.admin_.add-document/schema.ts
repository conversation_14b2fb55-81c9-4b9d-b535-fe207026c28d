import z from 'zod'

export const documentRecord = z.object({
  title: z.string().optional(),
  body: z.string().optional(),
  tags: z.string().optional(),
  is_classified: z.boolean(),
  category_id: z.string().min(1, 'Category is required'),
  files: z.array(z.instanceof(File)).optional(),

  added_date: z.string().min(1, 'Added date is required'),
})

export const inneihRecord = z.object({
  registration_no: z.string().optional(),
  mipa_hming: z.string().min(1, 'Mipa hming is required'),
  mipa_pa_hming: z.string().optional(),
  mipa_khua: z.string().optional(),
  hmeichhe_hming: z.string().min(1, 'Hmeichhe hming is required'),
  hmeichhe_pa_hming: z.string().optional(),
  hmeichhe_khua: z.string().optional(),
  hmun: z.string().optional(),
  inneih_ni: z.string().optional(),
  inneihtirtu: z.string().optional(),
  register_book_number: z.string().optional(),
})

export const baptismaRecord = z.object({
  registration_no: z.string().min(1, 'Registration no is required'),
  hming: z.string().min(1, 'Hming is required'),
  pa_hming: z.string().min(1, 'Pa hming is required'),
  nu_hming: z.string().optional(),
  khua: z.string().optional(),
  pian_ni: z.string().optional(),
  pian_ni_remarks: z.string().optional(),
  baptisma_chan_ni: z.string().optional(),
  baptisma_chan_ni_remarks: z.string().optional(),
  chantirtu: z.string().optional(),
  age: z.string().optional(),
  register_book_number: z.string().optional(),
})

export const inneihRecordSchema = z.object({
  inneih_record: inneihRecord,
  document: documentRecord,
})

export const baptismaRecordSchema = z.object({
  baptisma_record: baptismaRecord,
  document: documentRecord,
})

export const addRecordSchema = z.object({
  inneih_record: inneihRecord.optional(),
  baptisma_record: baptismaRecord.optional(),
  document: documentRecord,
})

export type InneihRecordType = z.infer<typeof inneihRecordSchema>
export type BaptismaRecordType = z.infer<typeof baptismaRecordSchema>
export type DocumentRecordType = z.infer<typeof documentRecord>
export type AddRecordType = z.infer<typeof addRecordSchema>
