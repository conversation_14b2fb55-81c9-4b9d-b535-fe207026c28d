import { NavLink, useLocation, useNavigate } from 'react-router'
import { toast } from 'sonner'
import LogoutIcon from '~/components/icons/logout-icon'
import { Button } from '~/components/ui/button'
import {
  Sidebar,
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from '~/components/ui/sidebar'
import useLogout from '~/hooks/use-logout'
import { parseGraphqlError } from '~/lib/parse-graphql-error'

const menu = [
  {
    title: 'Welcome',
    url: '/admin/welcome',
  },
  {
    title: 'Add Spotlight',
    url: '/admin/add-spotlight',
  },
  {
    title: 'Browse Spotlight',
    url: '/admin/browse-spotlight',
  },
  {
    title: 'Add Document',
    url: '/admin/add-document',
  },
  {
    title: 'Browse Documents',
    url: '/admin/browse-documents',
  },
  {
    title: 'Categories',
    url: '/admin/categories',
  },
  {
    title: 'Settings',
    url: '/admin/settings',
  },
]

export default function AdminSidebar() {
  const navigate = useNavigate()
  const location = useLocation()
  const { logout } = useLogout()

  const handleLogout = () => {
    logout.mutate(undefined, {
      onError: (error) => {
        toast.error(parseGraphqlError(error))
      },
      onSuccess: () => {
        navigate('/login')
      },
    })
  }

  return (
    <Sidebar>
      <SidebarContent className="flex flex-col">
        <SidebarGroup className="grow">
          <SidebarGroupContent>
            <SidebarMenu>
              {menu.map(item => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={item.url === location.pathname}
                  >
                    <NavLink to={item.url}>{item.title}</NavLink>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarMenu>
            <SidebarMenuItem>
              <Button
                onClick={handleLogout}
                isLoading={logout.isPending}
                variant="destructive"
                className="w-full"
              >
                <LogoutIcon className="mr-1" />
                Log Out
              </Button>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
    </Sidebar>
  )
}
