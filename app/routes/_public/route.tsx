import { Link, Outlet } from 'react-router'

export default function HomeLayout() {
  return (
    <div
      className={`
        mx-auto flex size-full min-h-screen w-full flex-col overflow-hidden
        bg-gray-100
      `}
    >
      <div className="flex h-24 w-full items-center bg-primary text-white">
        <div className="mx-auto flex w-full max-w-7xl">
          <div className="flex basis-1/3">
            <Link to="/">
              <img src="/pci_logo.png" className="ml-8 size-20" />
            </Link>
          </div>
          <div className="flex basis-1/3 items-center justify-center text-2xl">
            MIZORAM SYNOD ARCHIVE
          </div>
          <div className="flex basis-1/3 items-center justify-end">
            <Link to="/login">
              Login
            </Link>
          </div>
        </div>
      </div>
      <div className="mx-auto flex w-full max-w-7xl grow flex-col">
        <Outlet />
      </div>

      <footer>
        <div
          className={`
            mx-auto flex h-48 max-w-screen-xl justify-between bg-[#525252] px-8
            py-4 text-white
          `}
        >
          <div>
            <img src="/pci_logo.png" className="mb-4 h-20 w-20" />
            <div>Content Maintained and Updated by</div>
            <div>Synod Archives Department</div>
            <div>Copyright &#169; Mizoram Synod. All Right Reserved</div>
          </div>
          <div className="mr-8 flex items-center">
            <a
              rel="noopener noreferrer"
              href="https://www.arsi.in"
              target="_blank"
              aria-label="Arsi"
            >
              {/* <div className="mb-4 size-28 bg-white" /> */}
              <div className="flex flex-col items-center">
                <img src="/arsi_logo.svg" className="mb-4 size-16" />
                <div>
                  Developed by
                </div>
                <div>
                  Arsi Consultancy
                </div>
              </div>
            </a>
          </div>
        </div>
      </footer>
    </div>
  )
}
