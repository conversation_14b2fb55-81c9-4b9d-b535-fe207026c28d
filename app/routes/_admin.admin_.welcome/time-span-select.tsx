import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '~/components/ui/select'
import { TimeSpan } from '~/gql/graphql'

interface Props {
  value: string
  onChange: (value: TimeSpan) => void
}

export default function TimeSpanSelect({ value, onChange }: Props) {
  return (
    <Select value={value} onValueChange={onChange}>
      <SelectTrigger className="w-[300px]">
        <SelectValue placeholder="Select time span" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value={TimeSpan.AllTime}>All time</SelectItem>
        <SelectItem value={TimeSpan.CurrentYear}>Current year</SelectItem>
        <SelectItem value={TimeSpan.PastYear}>Past year</SelectItem>
        <SelectItem value={TimeSpan.PastMonth}>Past month</SelectItem>
        <SelectItem value={TimeSpan.PastWeek}>Past week</SelectItem>
        <SelectItem value={TimeSpan.Today}>Today</SelectItem>
      </SelectContent>
    </Select>
  )
}
