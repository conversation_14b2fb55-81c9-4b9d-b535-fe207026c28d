import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { TimeSpan } from '~/gql/graphql'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_SPOTLIGHT_VIEW_COUNT } from './graphql'

export default function useGetSpotlightViewCount() {
  const [page, setPage] = useState(1)
  const [timeSpan, setTimeSpan] = useState(TimeSpan.AllTime)

  const handlePage = (page: number) => {
    setPage(page)
  }

  const handleTimeSpan = (timeSpan: TimeSpan) => {
    setPage(1)
    setTimeSpan(timeSpan)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-spotlight-view-count', timeSpan, page],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_SPOTLIGHT_VIEW_COUNT,
        variables: {
          first: 10,
          page,
          time_span: timeSpan,
        },
      })
    },
  })

  const lastPage = data?.getSpotlightViewCount?.paginator_info?.last_page ?? 1

  return { data, isLoading, isError, timeSpan, page, handlePage, handleTimeSpan, lastPage }
}
