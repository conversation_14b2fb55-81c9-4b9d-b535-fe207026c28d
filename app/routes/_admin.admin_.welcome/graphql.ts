import { graphql } from '~/gql'

export const GET_SPOTLIGHT_VIEW_COUNT = graphql(`
  query GetSpotlightViewCount(
    $first: Int!
    $page: Int
    $time_span: TimeSpan!
  ) {
    getSpotlightViewCount(
      first: $first
      page: $page
      time_span: $time_span
    ) {
      data {
        id
        title
        body
        count
      }
      paginator_info {
        last_page
      }
    }
  }
`)

export const GET_DOCUMENT_SPOTLIGHT_COUNT = graphql(`
  query GetDocumentSpotlightCount {
    getDocumentSpotlightCount {
      document_count
      spotlight_count
    }
  }
`)

export const GET_DOCUMENT_VIEW_COUNT = graphql(`
  query GetDocumentViewCount(
    $first: Int!
    $page: Int
    $time_span: TimeSpan!
    $category_id: ID
  ) {
    getDocumentViewCount(
      first: $first
      page: $page
      time_span: $time_span
      category_id: $category_id
    ) {
      data {
        document_id
        document_title
        view_count
        child_category
        parent_category
      }
      paginator_info {
        last_page
      }
    }
  }
`)
