import PagePagination from '~/components/common/page-pagination'
import LoaderIcon from '~/components/icons/loader-icon'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import TimeSpanSelect from './time-span-select'
import useGetSpotlightViewCount from './use-get-spotlight-view-count'

export default function SpotlightViewCount() {
  const { data, isLoading, isError, timeSpan, page, handlePage, handleTimeSpan, lastPage } = useGetSpotlightViewCount()

  if (isError) {
    return <div className="rounded-md border p-4">Error loading spotlight view count</div>
  }

  return (
    <div className="rounded-md border border-black p-4">
      <div className="flex justify-between">
        <div className="font-bold">
          Spotlight View Count
        </div>
        <TimeSpanSelect value={timeSpan} onChange={handleTimeSpan} />
      </div>
      <div className="mt-8 flex grow flex-col gap-4">
        <div className="overflow-x-auto">
          <Table className="w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-3/4">
                  Title
                </TableHead>
                <TableHead className="w-1/4">
                  Visitor's count
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading && (
                <TableRow>
                  <TableCell colSpan={2}>
                    <div className="flex justify-center">
                      <LoaderIcon className="size-4 animate-spin" />
                    </div>
                  </TableCell>
                </TableRow>
              )}
              {!data?.getSpotlightViewCount?.data?.length && (
                <TableRow>
                  <TableCell colSpan={2}>
                    <div className="flex justify-center">
                      No data found
                    </div>
                  </TableCell>
                </TableRow>
              )}
              {data?.getSpotlightViewCount?.data?.map(item => (
                <TableRow key={item.id}>
                  <TableCell>
                    {item.title}
                  </TableCell>
                  <TableCell>
                    {item.count}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {lastPage > 1 && (
        <div className="my-4">
          <PagePagination
            currentPage={page}
            handlePagePagination={handlePage}
            lastPage={lastPage}
          />
        </div>
      )}
    </div>
  )
}
