import { ChevronsUpDown } from 'lucide-react'
import { startTransition } from 'react'
import PagePagination from '~/components/common/page-pagination'
import LoaderIcon from '~/components/icons/loader-icon'
import { Button } from '~/components/ui/button'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from '~/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '~/components/ui/popover'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '~/components/ui/table'
import useBoolean from '~/hooks/use-boolean'
import useGetCategories from '~/hooks/use-get-categories'
import TimeSpanSelect from './time-span-select'
import useGetDocumentViewCount from './use-get-document-view-count'

export default function DocumentViewCount() {
  const { isOpen, toggle } = useBoolean()
  const { data, isLoading: isLoadingDocumentViewCount, isError: isErrorDocumentViewCount, handleCategory, timeSpan, handleTimeSpan, page, handlePage, lastPage } = useGetDocumentViewCount()
  const { categories, isLoading, isError, keyword, handleKeyword } = useGetCategories()

  if (isErrorDocumentViewCount) {
    return <div className="rounded-md border p-4">Error loading document view count data</div>
  }

  return (
    <div className="rounded-md border border-black p-4">
      <div className="flex justify-between">
        <div className="font-bold">
          Document View Count
        </div>
        <div className="flex gap-x-4">
          <div>
            <Popover
              open={isOpen}
              onOpenChange={(open) => {
                // if (open) {
                //   handleKeyword('')
                // }
                toggle(open)
              }}
            >
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  role="combobox"
                  aria-expanded={isOpen}
                  className="w-[300px] justify-between"

                >
                  <span className="truncate">
                    {keyword ? categories?.find(category => category.name === keyword)?.name ?? '...' : 'Select category'}
                  </span>
                  <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-[300px] p-0">
                <Command>
                  <CommandInput
                    value={keyword}
                    isLoading={isLoading}
                    onValueChange={handleKeyword}
                    placeholder="Search category..."
                  />
                  <CommandList>
                    {!isLoading && <CommandEmpty>No category found.</CommandEmpty>}
                    {isError && (
                      <div className="py-6 text-center text-sm">
                        Error fetching category list.
                      </div>
                    )}
                    <CommandGroup>
                      {categories
                        && categories.map(category => (
                          <CommandItem
                            key={category.category_id}
                            value={category.name}
                            onSelect={(currentValue) => {
                              // Close popover immediately for better UX
                              toggle(false)
                              handleCategory(category.category_id)
                              // Use startTransition to mark keyword update as non-urgent
                              startTransition(() => {
                                handleKeyword(currentValue === keyword ? '' : currentValue)
                              })
                            }}
                          >
                            <span>{category.name}</span>
                          </CommandItem>
                        ),
                        )}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
          </div>

          <TimeSpanSelect value={timeSpan} onChange={handleTimeSpan} />

        </div>

      </div>
      <div className="mt-8 flex grow flex-col gap-4">
        <div className="overflow-x-auto">
          <Table className="w-full">
            <TableHeader>
              <TableRow>
                <TableHead className="w-1/2">
                  Title
                </TableHead>
                <TableHead className="w-1/4">
                  Visitor's count
                </TableHead>
                <TableHead className="w-1/4">
                  Category
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoadingDocumentViewCount && (
                <TableRow>
                  <TableCell colSpan={3}>
                    <div className="flex justify-center">
                      <LoaderIcon className="size-4 animate-spin" />
                    </div>
                  </TableCell>
                </TableRow>
              )}
              {!data?.getDocumentViewCount?.data?.length && (
                <TableRow>
                  <TableCell colSpan={3}>
                    <div className="flex justify-center">
                      No data found
                    </div>
                  </TableCell>
                </TableRow>
              )}
              {data?.getDocumentViewCount?.data?.map(item => (
                <TableRow key={item.document_id}>
                  <TableCell>
                    {item.document_title}
                  </TableCell>
                  <TableCell>
                    {item.view_count}
                  </TableCell>
                  <TableCell>
                    {item.parent_category}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
      {lastPage > 1 && (
        <div className="my-4">
          <PagePagination
            currentPage={page}
            handlePagePagination={handlePage}
            lastPage={lastPage}
          />

        </div>
      )}

    </div>
  )
}
