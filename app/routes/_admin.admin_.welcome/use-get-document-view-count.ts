import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { TimeSpan } from '~/gql/graphql'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_DOCUMENT_VIEW_COUNT } from './graphql'

export default function useGetDocumentViewCount() {
  const [page, setPage] = useState(1)
  const [timeSpan, setTimeSpan] = useState(TimeSpan.AllTime)
  const [categoryId, setCategoryId] = useState<string | undefined>(undefined)

  const handleCategory = (categoryId: string | undefined) => {
    setPage(1)
    setCategoryId(categoryId)
  }

  const handlePage = (page: number) => {
    setPage(page)
  }

  const handleTimeSpan = (timeSpan: TimeSpan) => {
    setPage(1)
    setTimeSpan(timeSpan)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-document-view-count', timeSpan, page, categoryId],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_DOCUMENT_VIEW_COUNT,
        variables: {
          first: 10,
          page,
          time_span: timeSpan,
          category_id: categoryId,
        },
      })
    },
  })

  const lastPage = data?.getDocumentViewCount?.paginator_info?.last_page ?? 1

  return { data, isLoading, isError, timeSpan, page, handlePage, handleTimeSpan, handleCategory, categoryId, lastPage }
}
