import { useQuery } from '@tanstack/react-query'
import { graphqlClient } from '~/lib/graphql-client'
import { GET_DOCUMENT_SPOTLIGHT_COUNT } from './graphql'

export default function useGetDocumentSpotlightCount() {
  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-document-spotlight-count'],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_DOCUMENT_SPOTLIGHT_COUNT,
      })
    },
  })

  return { data, isLoading, isError }
}
