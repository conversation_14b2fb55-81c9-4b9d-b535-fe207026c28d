import { graphql } from '~/gql'

export const GET_CATEGORIES = graphql(`
  query GetCategoriesHome(
    $keyword: String
  ) {
    getCategories(
      keyword: $keyword
    ) {
      id
      name
      parent_id
      is_leaf
      is_classified
      children {
        id
        name
        is_leaf
        is_classified
        children {
          id
          name
          is_leaf
          is_classified
        }
      }
    }
  }
`)

export const GET_SPOTLIGHT_BY_ID = graphql(`
  query GetSpotlightById($id: ID!) {
    getSpotlightById(id: $id) {
      id
    }
  }
`,
)
