import type { GetSpotlightsQuery } from '~/gql/graphql'
import { useState } from 'react'
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from '~/components/ui/carousel'
import useBoolean from '~/hooks/use-boolean'
import useGetSpotlights from '~/hooks/use-get-spotlights'
import { baseUrl } from '~/lib/base-url'
import SpotlightModal from './spotlight-modal'

export default function Spotlight() {
  const { data } = useGetSpotlights({ first: 10 })
  const { isOpen, toggle } = useBoolean()

  const [selectedSpotlight, setSelectedSpotlight] = useState<GetSpotlightsQuery['getSpotlights']['data'][number]>({
    id: '',
    title: '',
    body: '',
    file_path: '',
  })

  return (
    <>
      <div className="bg-gray-400 pt-6">
        <Carousel className="mx-64 h-72">
          <CarouselContent>
            {data?.getSpotlights?.data?.map(spotlight => (
              spotlight.file_path && (
                <CarouselItem
                  onClick={() => {
                    setSelectedSpotlight(spotlight)
                    toggle(true)
                  }}
                  className="grid grid-cols-2"
                  key={spotlight.id}
                >
                  <img
                    className="col-span-1 h-64 w-full object-cover"
                    src={`${baseUrl}/image/${spotlight.file_path}`}
                  />
                  <div className="col-span-1 flex flex-col gap-y-2 bg-white p-4">
                    <div className="text-xl font-semibold text-gold">{ spotlight.title }</div>
                    <div className="line-clamp-6 text-sm">
                      { spotlight.body }
                    </div>

                  </div>
                </CarouselItem>
              )
            ),
            )}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
      </div>
      {selectedSpotlight.id && (
        <SpotlightModal
          isOpen={isOpen}
          spotlight={selectedSpotlight}
          toggle={toggle}
        />
      )}
    </>
  )
}
