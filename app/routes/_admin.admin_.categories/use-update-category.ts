import { useMutation, useQueryClient } from '@tanstack/react-query'
import { toast } from 'sonner'
import { graphqlClient } from '~/lib/graphql-client'
import { parseGraphqlError } from '~/lib/parse-graphql-error'
import { UPDATE_CATEGORY, UPDATE_CATEGORY_CLASSIFIED_STATUS } from './graphql'

export default function useUpdateCategory() {
  const queryClient = useQueryClient()

  const updateClassified = useMutation({
    mutationFn: async ({ id, is_classified }: { id: string, is_classified: boolean }) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_CATEGORY_CLASSIFIED_STATUS,
        variables: {
          category_id: id,
          is_classified,
        },
      })
    },
    onSuccess: () => {
      toast.success('Category updated')
      queryClient.invalidateQueries({
        queryKey: ['get-categories'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  const updateName = useMutation({
    mutationFn: async ({ id, name }: { id: string, name: string }) => {
      const client = await graphqlClient()
      return client.request({
        document: UPDATE_CATEGORY,
        variables: {
          id,
          name,
        },
      })
    },
    onSuccess: () => {
      toast.success('Name updated')
      queryClient.invalidateQueries({
        queryKey: ['get-categories'],
      })
    },
    onError: (error) => {
      toast.error(parseGraphqlError(error))
    },
  })

  return { updateClassified, updateName }
}
