import { useQuery } from '@tanstack/react-query'
import { useState } from 'react'
import { GET_CATEGORIES } from '~/graphql/queries/get-categories'
import { graphqlClient } from '~/lib/graphql-client'

interface CategoryNode {
  id: string
  name: string
  parent?: {
    id: string
    name: string
    parent?: {
      id: string
      name: string
      parent?: {
        id: string
        name: string
      }
    }
  }
}

interface FlatCategory {
  category_id: string
  name: string
}

function buildCategoryPath(category: CategoryNode): string[] {
  const path: string[] = [category.name]
  let current = category.parent

  while (current) {
    // Skip "Default" parent names
    if (current.name !== 'Default') {
      path.unshift(current.name)
    }
    current = current.parent
  }

  return path
}

function flattenCategories(categories: CategoryNode[]): FlatCategory[] {
  const result: FlatCategory[] = []

  for (const category of categories) {
    const pathParts = buildCategoryPath(category)
    const name = pathParts.join(' | ')

    result.push({
      category_id: category.id,
      name,
    })
  }

  return result
}

export default function useGetCategories() {
  const [keyword, setKeyword] = useState('')

  const handleKeyword = (keyword: string) => {
    setKeyword(keyword)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['categories', keyword],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_CATEGORIES,
        variables: {
          keyword,
          unnested: true,
          only_leaf: true,
        },
      })
    },
  })

  // Flatten the nested categories
  const categories = data?.getCategories ? flattenCategories(data.getCategories as CategoryNode[]) : []

  return {
    categories,
    isLoading,
    isError,
    handleKeyword,
    keyword,
  }
}
