import { useQuery } from '@tanstack/react-query'
import { parseAsInteger, useQueryState } from 'nuqs'
import { GET_SPOTLIGHTS } from '~/graphql/queries/get-spotlights'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGetSpotlights({ first = 8 }: { first?: number }) {
  const [page, setPage] = useQueryState('page', parseAsInteger.withDefault(1))

  const handlePage = (page: number) => {
    setPage(page)
  }

  const { data, isLoading, isError } = useQuery({
    queryKey: ['get-spotlights', page],
    queryFn: async () => {
      const client = await graphqlClient()
      return client.request({
        document: GET_SPOTLIGHTS,
        variables: {
          first,
          page,
        },
      })
    },
  })

  const lastPage = data?.getSpotlights?.paginatorInfo?.lastPage ?? 1

  return { data, isLoading, isError, page, handlePage, lastPage }
}
