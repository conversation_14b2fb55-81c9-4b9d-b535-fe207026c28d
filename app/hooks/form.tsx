import { createFormHook, createFormHookContexts } from '@tanstack/react-form'
import FormMessage from '~/components/common/form-message'
import Tiptap from '~/components/common/tip-tap'
import { Button } from '~/components/ui/button'
import { Checkbox } from '~/components/ui/checkbox'
import { Input } from '~/components/ui/input'
import { Label } from '~/components/ui/label'
import { PillsInput } from '~/components/ui/pills-input'
import { Textarea } from '~/components/ui/textarea'
import { cn } from '~/lib/utils'

const { fieldContext, useFieldContext, formContext, useFormContext }
  = createFormHookContexts()

// Export the field context hook for use in custom components
export function InputRichText({ label }: { label: string }) {
  const field = useFieldContext<string>()

  return (
    <div className="flex flex-col items-start gap-y-2 text-sm font-medium">
      <div>{label}</div>
      <Tiptap
        value={field.state.value}
        onChange={e => field.handleChange(e)}
      />
      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </div>
  )
}

export function InputField({ label, type, placeholder, multiple }: { label: string, type?: string, placeholder?: string, multiple?: boolean }) {
  const field = useFieldContext<string>()
  return (
    <Label className="flex flex-col items-start gap-y-2">
      <div>{label}</div>
      <Input
        value={field.state.value}
        onChange={e => field.handleChange(e.target.value)}
        placeholder={placeholder}
        className={cn('bg-white', {
          block: type === 'date',
        })}
        type={type}
        multiple={multiple}
      />
      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </Label>
  )
}

export function CheckboxField({ label }: { label: string }) {
  const field = useFieldContext<boolean>()
  return (
    <Label className="flex items-center gap-x-2">
      <Checkbox
        className="bg-white"
        checked={field.state.value === true}
        onCheckedChange={checked => field.handleChange(!!checked)}
      />
      <div>{label}</div>
      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </Label>
  )
}

export function TextareaField({ label }: { label: string }) {
  const field = useFieldContext<string>()
  return (
    <Label className="flex flex-col items-start gap-y-2">
      <div>{label}</div>
      <Textarea
        value={field.state.value}
        onChange={e => field.handleChange(e.target.value)}
      />
      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </Label>
  )
}

export function MobileInputField({ label }: { label: string }) {
  const field = useFieldContext<string>()
  return (
    <Label className="flex flex-col items-start gap-y-2">
      <div>{label}</div>
      <Input
        value={field.state.value}
        onChange={e => field.handleChange(e.target.value)}
        type="tel"
        maxLength={10}
      />
      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </Label>
  )
}

export function SubmitButton({ label }: { label: string }) {
  const form = useFormContext()
  return (
    <form.Subscribe selector={state => state.isSubmitting}>
      {isSubmitting => <Button isLoading={isSubmitting}>{label}</Button>}
    </form.Subscribe>
  )
}

export function PillInput({ label }: { label: string }) {
  const field = useFieldContext<string>()
  return (
    <Label className="flex flex-col items-start gap-y-2">
      <div>{label}</div>
      <PillsInput
        value={field.state.value}
        onChange={e => field.handleChange(e)}
      />
      <FormMessage errors={field.state.meta.errors.map(e => e?.message).join(', ')} />
    </Label>
  )
}

export const { useAppForm, withForm } = createFormHook({
  fieldComponents: {
    InputField,
    MobileInputField,
    CheckboxField,
    InputRichText,
    PillInput,
    TextareaField,
  },
  formComponents: {
    SubmitButton,
  },
  fieldContext,
  formContext,
})
