import { useMutation } from '@tanstack/react-query'
import { GENERATE_DOWNLOAD_LINK } from '~/graphql/mutation/generate-download-link'
import { graphqlClient } from '~/lib/graphql-client'

export default function useGenerateDownloadLink() {
  const generateDownloadLink = useMutation({
    mutationFn: async (id: string) => {
      const client = await graphqlClient()
      return await client.request({
        document: GENERATE_DOWNLOAD_LINK,
        variables: {
          doc_file_id: id,
        },
      })
    },
  })

  return { generateDownloadLink }
}
