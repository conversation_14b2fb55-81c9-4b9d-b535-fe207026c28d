import type { BaptismaRecordFragmentFragment, InneihRecordFragmentFragment } from '~/gql/graphql'
import { useFragment } from '~/gql'
import { BAPTISMA_RECORD_FRAGMENT, INNEIH_RECORD_FRAGMENT } from '~/routes/_public.category_.$id/graphql'

interface InneihRecordDisplayProps {
  data: InneihRecordFragmentFragment
}

interface BaptismaRecordDisplayProps {
  data: BaptismaRecordFragmentFragment
}

export function InneihRecordDisplay({ data }: InneihRecordDisplayProps) {
  // Use the fragment to get typed data
  const inneihRecord = useFragment(INNEIH_RECORD_FRAGMENT, data)

  return (
    <div className="space-y-2 rounded-lg border p-4">
      <h3 className="font-semibold">Inneih Record</h3>
      <div className="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span className="font-medium">Registration No:</span>
          {' '}
          {inneihRecord.inneih_registration_no || '-'}
        </div>
        <div>
          <span className="font-medium">Mipa Hming:</span>
          {' '}
          {inneihRecord.mipa_hming}
        </div>
        <div>
          <span className="font-medium">Mipa Pa Hming:</span>
          {' '}
          {inneihRecord.mipa_pa_hming || '-'}
        </div>
        <div>
          <span className="font-medium">Hmeichhe Hming:</span>
          {' '}
          {inneihRecord.hmeichhe_hming}
        </div>
        <div>
          <span className="font-medium">Hmeichhe Pa Hming:</span>
          {' '}
          {inneihRecord.hmeichhe_pa_hming || '-'}
        </div>
        <div>
          <span className="font-medium">Inneih Ni:</span>
          {' '}
          {inneihRecord.inneih_ni || '-'}
        </div>
        <div>
          <span className="font-medium">Hmun:</span>
          {' '}
          {inneihRecord.hmun || '-'}
        </div>
        <div>
          <span className="font-medium">Inneihtirtu:</span>
          {' '}
          {inneihRecord.inneihtirtu || '-'}
        </div>
      </div>
    </div>
  )
}

export function BaptismaRecordDisplay({ data }: BaptismaRecordDisplayProps) {
  // Use the fragment to get typed data
  const baptismaRecord = useFragment(BAPTISMA_RECORD_FRAGMENT, data)

  return (
    <div className="space-y-2 rounded-lg border p-4">
      <h3 className="font-semibold">Baptisma Record</h3>
      <div className="grid grid-cols-2 gap-2 text-sm">
        <div>
          <span className="font-medium">Registration No:</span>
          {' '}
          {baptismaRecord.baptisma_registration_no}
        </div>
        <div>
          <span className="font-medium">Hming:</span>
          {' '}
          {baptismaRecord.hming}
        </div>
        <div>
          <span className="font-medium">Pa Hming:</span>
          {' '}
          {baptismaRecord.pa_hming}
        </div>
        <div>
          <span className="font-medium">Nu Hming:</span>
          {' '}
          {baptismaRecord.nu_hming || '-'}
        </div>
        <div>
          <span className="font-medium">Pian Ni:</span>
          {' '}
          {baptismaRecord.pian_ni || '-'}
        </div>
        <div>
          <span className="font-medium">Baptisma Chan Ni:</span>
          {' '}
          {baptismaRecord.baptisma_chan_ni || '-'}
        </div>
        <div>
          <span className="font-medium">Khua:</span>
          {' '}
          {baptismaRecord.khua || '-'}
        </div>
        <div>
          <span className="font-medium">Chantirtu:</span>
          {' '}
          {baptismaRecord.chantirtu || '-'}
        </div>
      </div>
    </div>
  )
}

// Example usage component that shows how to use these with query data
interface RecordDisplayProps {
  extraRecord: {
    __typename?: 'InneihRecord' | 'BaptismaRecord'
  } & (InneihRecordFragmentFragment | BaptismaRecordFragmentFragment)
}

export function RecordDisplay({ extraRecord }: RecordDisplayProps) {
  if (extraRecord.__typename === 'InneihRecord') {
    return <InneihRecordDisplay data={extraRecord as InneihRecordFragmentFragment} />
  }

  if (extraRecord.__typename === 'BaptismaRecord') {
    return <BaptismaRecordDisplay data={extraRecord as BaptismaRecordFragmentFragment} />
  }

  return null
}
