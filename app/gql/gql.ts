/* eslint-disable */
import * as types from './graphql';
import type { TypedDocumentNode as DocumentNode } from '@graphql-typed-document-node/core';

/**
 * Map of all GraphQL operations in the project.
 *
 * This map has several performance disadvantages:
 * 1. It is not tree-shakeable, so it will include all operations in the project.
 * 2. It is not minifiable, so the string of a GraphQL query will be multiple times inside the bundle.
 * 3. It does not support dead code elimination, so it will add unused operations.
 *
 * Therefore it is highly recommended to use the babel or swc plugin for production.
 * Learn more about it here: https://the-guild.dev/graphql/codegen/plugins/presets/preset-client#reducing-bundle-size
 */
type Documents = {
    "\n  mutation GenerateDownloadLink(\n    $doc_file_id: ID!\n  ) {\n    generateDownloadLink(\n      doc_file_id: $doc_file_id\n    )\n  }\n": typeof types.GenerateDownloadLinkDocument,
    "\n  mutation Logout {\n    logout \n  }\n": typeof types.LogoutDocument,
    "\n  query GetCategories(\n    $keyword: String\n    $only_leaf: Boolean\n    $unnested: Boolean\n  ) {\n    getCategories(\n      keyword: $keyword\n      only_leaf: $only_leaf\n      unnested: $unnested\n    ) {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n": typeof types.GetCategoriesDocument,
    "\n  query GetSpotlights(\n    $first: Int!\n    $page: Int\n  ) {\n    getSpotlights(\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        title\n        body\n        file_path\n      }\n      paginatorInfo {\n        lastPage\n      }\n    }\n  }\n": typeof types.GetSpotlightsDocument,
    "\n  mutation AddRecord(\n    $inneih_record: InneihRecordInput\n    $baptisma_record: BaptismaRecordInput\n    $document: DocumentInput!\n  ) {\n    addRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    ) \n  }\n": typeof types.AddRecordDocument,
    "\n  mutation AddSpotlight(\n    $body: String!\n    $image: Upload\n    $title: String!\n  ) {\n    addSpotlight(\n      body: $body\n      image: $image\n      title: $title\n    )\n  }\n": typeof types.AddSpotlightDocument,
    "\n  query GetDocuments(\n    $first: Int!\n    $page: Int\n    $added_date: Date\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocuments(\n      first: $first\n      page: $page\n      added_date: $added_date\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      data {\n        id\n        title\n        body\n        tags\n        added_date\n        category_id\n        is_classified\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            id\n            inneih_registration_no: registration_no\n            mipa_hming\n            mipa_pa_hming\n            mipa_khua\n            hmeichhe_hming\n            hmeichhe_pa_hming\n            hmeichhe_khua\n            inneih_ni\n            hmun\n            inneihtirtu\n            register_book_number\n          }\n          ... on BaptismaRecord {\n            id\n            baptisma_registration_no: registration_no\n            hming\n            pa_hming\n            nu_hming\n            pian_ni\n            pian_ni_remarks\n            baptisma_chan_ni\n            baptisma_chan_ni_remarks\n            khua\n            chantirtu\n            register_book_number\n            age\n          }\n        }\n        files {\n          id\n          doc_id\n          path\n        }\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n": typeof types.GetDocumentsDocument,
    "\n  mutation UpdateRecord(\n    $inneih_record: InneihRecordUpdateInput\n    $baptisma_record: BaptismaRecordUpdateInput\n    $document: DocumentUpdateInput!\n  ) {\n    updateRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    )\n  }\n": typeof types.UpdateRecordDocument,
    "\n  mutation DeleteDocument(\n    $document_id: ID!\n  ) {\n    deleteDocument(\n      document_id: $document_id\n    )\n  }\n": typeof types.DeleteDocumentDocument,
    "\n  mutation DeleteFile(\n    $id: ID!\n  ) {\n    deleteDocumentFileById(\n      document_file_id: $id\n    )\n  }\n": typeof types.DeleteFileDocument,
    "\n  mutation DeleteSpotlight(\n    $id: ID!\n  ) {\n    deleteSpotlight(\n      id: $id\n    ) {\n      id\n    }\n  }\n": typeof types.DeleteSpotlightDocument,
    "\n  mutation UpdateSpotlight(\n    $id: ID!\n    $body: String\n    $image: Upload\n    $title: String\n  ) {\n    updateSpotlight(\n      id: $id\n      body: $body\n      image: $image\n      title: $title\n    ) \n  }\n": typeof types.UpdateSpotlightDocument,
    "\n  query GetCategoriesList(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n": typeof types.GetCategoriesListDocument,
    "\n  mutation UpdateCategory(\n    $id: ID!\n    $is_classified: Boolean\n    $name: String\n    $parent_id: ID\n  ) {\n    updateCategory(\n      id: $id\n      is_classified: $is_classified\n      name: $name\n      parent_id: $parent_id\n    ) \n  } \n": typeof types.UpdateCategoryDocument,
    "\n  mutation UpdateCategoryClassifiedStatus(\n    $category_id: ID!\n    $is_classified: Boolean!\n  ) {\n    updateCategoryClassifiedStatus(\n      category_id: $category_id\n      is_classified: $is_classified\n    ) \n  }\n": typeof types.UpdateCategoryClassifiedStatusDocument,
    "\n  mutation AddCategory(\n    $name: String!\n    $parent_id: ID\n    $is_classified: Boolean\n  ) {\n    addCategory(\n      name: $name\n      parent_id: $parent_id\n      is_classified: $is_classified\n    )\n  }\n": typeof types.AddCategoryDocument,
    "\n  mutation DeleteCategory(\n    $id: ID!\n  ) {\n    deleteCategory(\n      id: $id\n    ) {\n      id\n    }\n  }\n": typeof types.DeleteCategoryDocument,
    "\n  query GetSetting(\n    $name: SettingsKey!\n  ) {\n    getSetting(\n      name: $name\n    ) {\n      id\n      name\n      value\n    }\n  }\n": typeof types.GetSettingDocument,
    "\n  mutation UpsertSetting(\n    $key: SettingsKey!\n    $value: String!\n  ) {\n    upsertSetting(\n      key: $key\n      value: $value\n    )\n  }\n": typeof types.UpsertSettingDocument,
    "\n  query GetSpotlightViewCount(\n    $first: Int!\n    $page: Int\n    $time_span: TimeSpan!\n  ) {\n    getSpotlightViewCount(\n      first: $first\n      page: $page\n      time_span: $time_span\n    ) {\n      data {\n        id\n        title\n        body\n        count\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n": typeof types.GetSpotlightViewCountDocument,
    "\n  query GetDocumentSpotlightCount {\n    getDocumentSpotlightCount {\n      document_count\n      spotlight_count\n    }\n  }\n": typeof types.GetDocumentSpotlightCountDocument,
    "\n  query GetDocumentViewCount(\n    $first: Int!\n    $page: Int\n    $time_span: TimeSpan!\n    $category_id: ID\n  ) {\n    getDocumentViewCount(\n      first: $first\n      page: $page\n      time_span: $time_span\n      category_id: $category_id\n    ) {\n      data {\n        document_id\n        document_title\n        view_count\n        child_category\n        parent_category\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n": typeof types.GetDocumentViewCountDocument,
    "\n  query GetCategoriesHome(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      parent_id\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n": typeof types.GetCategoriesHomeDocument,
    "\n  query GetSpotlightById($id: ID!) {\n    getSpotlightById(id: $id) {\n      id\n    }\n  }\n": typeof types.GetSpotlightByIdDocument,
    "\n  fragment InneihRecordFragment on InneihRecord {\n    id\n    inneih_registration_no: registration_no\n    mipa_hming\n    mipa_pa_hming\n    mipa_khua\n    hmeichhe_hming\n    hmeichhe_pa_hming\n    hmeichhe_khua\n    inneih_ni\n    hmun\n    inneihtirtu\n    register_book_number\n  }\n": typeof types.InneihRecordFragmentFragmentDoc,
    "\n  fragment BaptismaRecordFragment on BaptismaRecord {\n    id\n    baptisma_registration_no: registration_no\n    hming\n    pa_hming\n    nu_hming\n    pian_ni\n    pian_ni_remarks\n    baptisma_chan_ni\n    baptisma_chan_ni_remarks\n    khua\n    chantirtu\n    age\n    register_book_number\n  }\n": typeof types.BaptismaRecordFragmentFragmentDoc,
    "\n  query GetDocumentsByCategoryId(\n    $first: Int!\n    $page: Int\n    $category_id: ID!\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocumentsByCategoryId(\n      first: $first\n      page: $page\n      category_id: $category_id\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      paginator_info {\n        last_page\n      }\n      data {\n        id\n        title\n        added_date\n        category {\n          id\n          name\n        }\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            ...InneihRecordFragment\n          }\n          ... on BaptismaRecord {\n            ...BaptismaRecordFragment\n          }\n        }\n      }\n    }\n  }\n": typeof types.GetDocumentsByCategoryIdDocument,
    "\n  query GetCategoryName(\n    $id: ID!\n  ) {\n    getCategoryById(\n      id: $id\n    ) {\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n": typeof types.GetCategoryNameDocument,
    "\n  query GetDocumentById($id: ID!) {\n    getDocumentById(id: $id) {\n    id\n    title\n    body\n    tags\n    added_date\n    category {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n    extra_record {\n      __typename\n      ... on InneihRecord {\n        ...InneihRecordFragment\n      }\n      ... on BaptismaRecord {\n        ...BaptismaRecordFragment\n      }\n    }\n    files {\n      id\n      path\n      file_type\n    }\n\n    }\n  }\n": typeof types.GetDocumentByIdDocument,
    "\n  mutation Login(\n    $name: String!\n    $password: String!\n  ) {\n    login(name: $name, password: $password) {\n      token\n      exp_date\n    }\n  }\n": typeof types.LoginDocument,
};
const documents: Documents = {
    "\n  mutation GenerateDownloadLink(\n    $doc_file_id: ID!\n  ) {\n    generateDownloadLink(\n      doc_file_id: $doc_file_id\n    )\n  }\n": types.GenerateDownloadLinkDocument,
    "\n  mutation Logout {\n    logout \n  }\n": types.LogoutDocument,
    "\n  query GetCategories(\n    $keyword: String\n    $only_leaf: Boolean\n    $unnested: Boolean\n  ) {\n    getCategories(\n      keyword: $keyword\n      only_leaf: $only_leaf\n      unnested: $unnested\n    ) {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n": types.GetCategoriesDocument,
    "\n  query GetSpotlights(\n    $first: Int!\n    $page: Int\n  ) {\n    getSpotlights(\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        title\n        body\n        file_path\n      }\n      paginatorInfo {\n        lastPage\n      }\n    }\n  }\n": types.GetSpotlightsDocument,
    "\n  mutation AddRecord(\n    $inneih_record: InneihRecordInput\n    $baptisma_record: BaptismaRecordInput\n    $document: DocumentInput!\n  ) {\n    addRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    ) \n  }\n": types.AddRecordDocument,
    "\n  mutation AddSpotlight(\n    $body: String!\n    $image: Upload\n    $title: String!\n  ) {\n    addSpotlight(\n      body: $body\n      image: $image\n      title: $title\n    )\n  }\n": types.AddSpotlightDocument,
    "\n  query GetDocuments(\n    $first: Int!\n    $page: Int\n    $added_date: Date\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocuments(\n      first: $first\n      page: $page\n      added_date: $added_date\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      data {\n        id\n        title\n        body\n        tags\n        added_date\n        category_id\n        is_classified\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            id\n            inneih_registration_no: registration_no\n            mipa_hming\n            mipa_pa_hming\n            mipa_khua\n            hmeichhe_hming\n            hmeichhe_pa_hming\n            hmeichhe_khua\n            inneih_ni\n            hmun\n            inneihtirtu\n            register_book_number\n          }\n          ... on BaptismaRecord {\n            id\n            baptisma_registration_no: registration_no\n            hming\n            pa_hming\n            nu_hming\n            pian_ni\n            pian_ni_remarks\n            baptisma_chan_ni\n            baptisma_chan_ni_remarks\n            khua\n            chantirtu\n            register_book_number\n            age\n          }\n        }\n        files {\n          id\n          doc_id\n          path\n        }\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n": types.GetDocumentsDocument,
    "\n  mutation UpdateRecord(\n    $inneih_record: InneihRecordUpdateInput\n    $baptisma_record: BaptismaRecordUpdateInput\n    $document: DocumentUpdateInput!\n  ) {\n    updateRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    )\n  }\n": types.UpdateRecordDocument,
    "\n  mutation DeleteDocument(\n    $document_id: ID!\n  ) {\n    deleteDocument(\n      document_id: $document_id\n    )\n  }\n": types.DeleteDocumentDocument,
    "\n  mutation DeleteFile(\n    $id: ID!\n  ) {\n    deleteDocumentFileById(\n      document_file_id: $id\n    )\n  }\n": types.DeleteFileDocument,
    "\n  mutation DeleteSpotlight(\n    $id: ID!\n  ) {\n    deleteSpotlight(\n      id: $id\n    ) {\n      id\n    }\n  }\n": types.DeleteSpotlightDocument,
    "\n  mutation UpdateSpotlight(\n    $id: ID!\n    $body: String\n    $image: Upload\n    $title: String\n  ) {\n    updateSpotlight(\n      id: $id\n      body: $body\n      image: $image\n      title: $title\n    ) \n  }\n": types.UpdateSpotlightDocument,
    "\n  query GetCategoriesList(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n": types.GetCategoriesListDocument,
    "\n  mutation UpdateCategory(\n    $id: ID!\n    $is_classified: Boolean\n    $name: String\n    $parent_id: ID\n  ) {\n    updateCategory(\n      id: $id\n      is_classified: $is_classified\n      name: $name\n      parent_id: $parent_id\n    ) \n  } \n": types.UpdateCategoryDocument,
    "\n  mutation UpdateCategoryClassifiedStatus(\n    $category_id: ID!\n    $is_classified: Boolean!\n  ) {\n    updateCategoryClassifiedStatus(\n      category_id: $category_id\n      is_classified: $is_classified\n    ) \n  }\n": types.UpdateCategoryClassifiedStatusDocument,
    "\n  mutation AddCategory(\n    $name: String!\n    $parent_id: ID\n    $is_classified: Boolean\n  ) {\n    addCategory(\n      name: $name\n      parent_id: $parent_id\n      is_classified: $is_classified\n    )\n  }\n": types.AddCategoryDocument,
    "\n  mutation DeleteCategory(\n    $id: ID!\n  ) {\n    deleteCategory(\n      id: $id\n    ) {\n      id\n    }\n  }\n": types.DeleteCategoryDocument,
    "\n  query GetSetting(\n    $name: SettingsKey!\n  ) {\n    getSetting(\n      name: $name\n    ) {\n      id\n      name\n      value\n    }\n  }\n": types.GetSettingDocument,
    "\n  mutation UpsertSetting(\n    $key: SettingsKey!\n    $value: String!\n  ) {\n    upsertSetting(\n      key: $key\n      value: $value\n    )\n  }\n": types.UpsertSettingDocument,
    "\n  query GetSpotlightViewCount(\n    $first: Int!\n    $page: Int\n    $time_span: TimeSpan!\n  ) {\n    getSpotlightViewCount(\n      first: $first\n      page: $page\n      time_span: $time_span\n    ) {\n      data {\n        id\n        title\n        body\n        count\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n": types.GetSpotlightViewCountDocument,
    "\n  query GetDocumentSpotlightCount {\n    getDocumentSpotlightCount {\n      document_count\n      spotlight_count\n    }\n  }\n": types.GetDocumentSpotlightCountDocument,
    "\n  query GetDocumentViewCount(\n    $first: Int!\n    $page: Int\n    $time_span: TimeSpan!\n    $category_id: ID\n  ) {\n    getDocumentViewCount(\n      first: $first\n      page: $page\n      time_span: $time_span\n      category_id: $category_id\n    ) {\n      data {\n        document_id\n        document_title\n        view_count\n        child_category\n        parent_category\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n": types.GetDocumentViewCountDocument,
    "\n  query GetCategoriesHome(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      parent_id\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n": types.GetCategoriesHomeDocument,
    "\n  query GetSpotlightById($id: ID!) {\n    getSpotlightById(id: $id) {\n      id\n    }\n  }\n": types.GetSpotlightByIdDocument,
    "\n  fragment InneihRecordFragment on InneihRecord {\n    id\n    inneih_registration_no: registration_no\n    mipa_hming\n    mipa_pa_hming\n    mipa_khua\n    hmeichhe_hming\n    hmeichhe_pa_hming\n    hmeichhe_khua\n    inneih_ni\n    hmun\n    inneihtirtu\n    register_book_number\n  }\n": types.InneihRecordFragmentFragmentDoc,
    "\n  fragment BaptismaRecordFragment on BaptismaRecord {\n    id\n    baptisma_registration_no: registration_no\n    hming\n    pa_hming\n    nu_hming\n    pian_ni\n    pian_ni_remarks\n    baptisma_chan_ni\n    baptisma_chan_ni_remarks\n    khua\n    chantirtu\n    age\n    register_book_number\n  }\n": types.BaptismaRecordFragmentFragmentDoc,
    "\n  query GetDocumentsByCategoryId(\n    $first: Int!\n    $page: Int\n    $category_id: ID!\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocumentsByCategoryId(\n      first: $first\n      page: $page\n      category_id: $category_id\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      paginator_info {\n        last_page\n      }\n      data {\n        id\n        title\n        added_date\n        category {\n          id\n          name\n        }\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            ...InneihRecordFragment\n          }\n          ... on BaptismaRecord {\n            ...BaptismaRecordFragment\n          }\n        }\n      }\n    }\n  }\n": types.GetDocumentsByCategoryIdDocument,
    "\n  query GetCategoryName(\n    $id: ID!\n  ) {\n    getCategoryById(\n      id: $id\n    ) {\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n": types.GetCategoryNameDocument,
    "\n  query GetDocumentById($id: ID!) {\n    getDocumentById(id: $id) {\n    id\n    title\n    body\n    tags\n    added_date\n    category {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n    extra_record {\n      __typename\n      ... on InneihRecord {\n        ...InneihRecordFragment\n      }\n      ... on BaptismaRecord {\n        ...BaptismaRecordFragment\n      }\n    }\n    files {\n      id\n      path\n      file_type\n    }\n\n    }\n  }\n": types.GetDocumentByIdDocument,
    "\n  mutation Login(\n    $name: String!\n    $password: String!\n  ) {\n    login(name: $name, password: $password) {\n      token\n      exp_date\n    }\n  }\n": types.LoginDocument,
};

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 *
 *
 * @example
 * ```ts
 * const query = graphql(`query GetUser($id: ID!) { user(id: $id) { name } }`);
 * ```
 *
 * The query argument is unknown!
 * Please regenerate the types.
 */
export function graphql(source: string): unknown;

/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation GenerateDownloadLink(\n    $doc_file_id: ID!\n  ) {\n    generateDownloadLink(\n      doc_file_id: $doc_file_id\n    )\n  }\n"): (typeof documents)["\n  mutation GenerateDownloadLink(\n    $doc_file_id: ID!\n  ) {\n    generateDownloadLink(\n      doc_file_id: $doc_file_id\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Logout {\n    logout \n  }\n"): (typeof documents)["\n  mutation Logout {\n    logout \n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCategories(\n    $keyword: String\n    $only_leaf: Boolean\n    $unnested: Boolean\n  ) {\n    getCategories(\n      keyword: $keyword\n      only_leaf: $only_leaf\n      unnested: $unnested\n    ) {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCategories(\n    $keyword: String\n    $only_leaf: Boolean\n    $unnested: Boolean\n  ) {\n    getCategories(\n      keyword: $keyword\n      only_leaf: $only_leaf\n      unnested: $unnested\n    ) {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSpotlights(\n    $first: Int!\n    $page: Int\n  ) {\n    getSpotlights(\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        title\n        body\n        file_path\n      }\n      paginatorInfo {\n        lastPage\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetSpotlights(\n    $first: Int!\n    $page: Int\n  ) {\n    getSpotlights(\n      first: $first\n      page: $page\n    ) {\n      data {\n        id\n        title\n        body\n        file_path\n      }\n      paginatorInfo {\n        lastPage\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddRecord(\n    $inneih_record: InneihRecordInput\n    $baptisma_record: BaptismaRecordInput\n    $document: DocumentInput!\n  ) {\n    addRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    ) \n  }\n"): (typeof documents)["\n  mutation AddRecord(\n    $inneih_record: InneihRecordInput\n    $baptisma_record: BaptismaRecordInput\n    $document: DocumentInput!\n  ) {\n    addRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    ) \n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddSpotlight(\n    $body: String!\n    $image: Upload\n    $title: String!\n  ) {\n    addSpotlight(\n      body: $body\n      image: $image\n      title: $title\n    )\n  }\n"): (typeof documents)["\n  mutation AddSpotlight(\n    $body: String!\n    $image: Upload\n    $title: String!\n  ) {\n    addSpotlight(\n      body: $body\n      image: $image\n      title: $title\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDocuments(\n    $first: Int!\n    $page: Int\n    $added_date: Date\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocuments(\n      first: $first\n      page: $page\n      added_date: $added_date\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      data {\n        id\n        title\n        body\n        tags\n        added_date\n        category_id\n        is_classified\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            id\n            inneih_registration_no: registration_no\n            mipa_hming\n            mipa_pa_hming\n            mipa_khua\n            hmeichhe_hming\n            hmeichhe_pa_hming\n            hmeichhe_khua\n            inneih_ni\n            hmun\n            inneihtirtu\n            register_book_number\n          }\n          ... on BaptismaRecord {\n            id\n            baptisma_registration_no: registration_no\n            hming\n            pa_hming\n            nu_hming\n            pian_ni\n            pian_ni_remarks\n            baptisma_chan_ni\n            baptisma_chan_ni_remarks\n            khua\n            chantirtu\n            register_book_number\n            age\n          }\n        }\n        files {\n          id\n          doc_id\n          path\n        }\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetDocuments(\n    $first: Int!\n    $page: Int\n    $added_date: Date\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocuments(\n      first: $first\n      page: $page\n      added_date: $added_date\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      data {\n        id\n        title\n        body\n        tags\n        added_date\n        category_id\n        is_classified\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            id\n            inneih_registration_no: registration_no\n            mipa_hming\n            mipa_pa_hming\n            mipa_khua\n            hmeichhe_hming\n            hmeichhe_pa_hming\n            hmeichhe_khua\n            inneih_ni\n            hmun\n            inneihtirtu\n            register_book_number\n          }\n          ... on BaptismaRecord {\n            id\n            baptisma_registration_no: registration_no\n            hming\n            pa_hming\n            nu_hming\n            pian_ni\n            pian_ni_remarks\n            baptisma_chan_ni\n            baptisma_chan_ni_remarks\n            khua\n            chantirtu\n            register_book_number\n            age\n          }\n        }\n        files {\n          id\n          doc_id\n          path\n        }\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateRecord(\n    $inneih_record: InneihRecordUpdateInput\n    $baptisma_record: BaptismaRecordUpdateInput\n    $document: DocumentUpdateInput!\n  ) {\n    updateRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    )\n  }\n"): (typeof documents)["\n  mutation UpdateRecord(\n    $inneih_record: InneihRecordUpdateInput\n    $baptisma_record: BaptismaRecordUpdateInput\n    $document: DocumentUpdateInput!\n  ) {\n    updateRecord(\n      inneih_record: $inneih_record\n      baptisma_record: $baptisma_record\n      document: $document\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteDocument(\n    $document_id: ID!\n  ) {\n    deleteDocument(\n      document_id: $document_id\n    )\n  }\n"): (typeof documents)["\n  mutation DeleteDocument(\n    $document_id: ID!\n  ) {\n    deleteDocument(\n      document_id: $document_id\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteFile(\n    $id: ID!\n  ) {\n    deleteDocumentFileById(\n      document_file_id: $id\n    )\n  }\n"): (typeof documents)["\n  mutation DeleteFile(\n    $id: ID!\n  ) {\n    deleteDocumentFileById(\n      document_file_id: $id\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteSpotlight(\n    $id: ID!\n  ) {\n    deleteSpotlight(\n      id: $id\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteSpotlight(\n    $id: ID!\n  ) {\n    deleteSpotlight(\n      id: $id\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateSpotlight(\n    $id: ID!\n    $body: String\n    $image: Upload\n    $title: String\n  ) {\n    updateSpotlight(\n      id: $id\n      body: $body\n      image: $image\n      title: $title\n    ) \n  }\n"): (typeof documents)["\n  mutation UpdateSpotlight(\n    $id: ID!\n    $body: String\n    $image: Upload\n    $title: String\n  ) {\n    updateSpotlight(\n      id: $id\n      body: $body\n      image: $image\n      title: $title\n    ) \n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCategoriesList(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCategoriesList(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateCategory(\n    $id: ID!\n    $is_classified: Boolean\n    $name: String\n    $parent_id: ID\n  ) {\n    updateCategory(\n      id: $id\n      is_classified: $is_classified\n      name: $name\n      parent_id: $parent_id\n    ) \n  } \n"): (typeof documents)["\n  mutation UpdateCategory(\n    $id: ID!\n    $is_classified: Boolean\n    $name: String\n    $parent_id: ID\n  ) {\n    updateCategory(\n      id: $id\n      is_classified: $is_classified\n      name: $name\n      parent_id: $parent_id\n    ) \n  } \n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpdateCategoryClassifiedStatus(\n    $category_id: ID!\n    $is_classified: Boolean!\n  ) {\n    updateCategoryClassifiedStatus(\n      category_id: $category_id\n      is_classified: $is_classified\n    ) \n  }\n"): (typeof documents)["\n  mutation UpdateCategoryClassifiedStatus(\n    $category_id: ID!\n    $is_classified: Boolean!\n  ) {\n    updateCategoryClassifiedStatus(\n      category_id: $category_id\n      is_classified: $is_classified\n    ) \n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation AddCategory(\n    $name: String!\n    $parent_id: ID\n    $is_classified: Boolean\n  ) {\n    addCategory(\n      name: $name\n      parent_id: $parent_id\n      is_classified: $is_classified\n    )\n  }\n"): (typeof documents)["\n  mutation AddCategory(\n    $name: String!\n    $parent_id: ID\n    $is_classified: Boolean\n  ) {\n    addCategory(\n      name: $name\n      parent_id: $parent_id\n      is_classified: $is_classified\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation DeleteCategory(\n    $id: ID!\n  ) {\n    deleteCategory(\n      id: $id\n    ) {\n      id\n    }\n  }\n"): (typeof documents)["\n  mutation DeleteCategory(\n    $id: ID!\n  ) {\n    deleteCategory(\n      id: $id\n    ) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSetting(\n    $name: SettingsKey!\n  ) {\n    getSetting(\n      name: $name\n    ) {\n      id\n      name\n      value\n    }\n  }\n"): (typeof documents)["\n  query GetSetting(\n    $name: SettingsKey!\n  ) {\n    getSetting(\n      name: $name\n    ) {\n      id\n      name\n      value\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation UpsertSetting(\n    $key: SettingsKey!\n    $value: String!\n  ) {\n    upsertSetting(\n      key: $key\n      value: $value\n    )\n  }\n"): (typeof documents)["\n  mutation UpsertSetting(\n    $key: SettingsKey!\n    $value: String!\n  ) {\n    upsertSetting(\n      key: $key\n      value: $value\n    )\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSpotlightViewCount(\n    $first: Int!\n    $page: Int\n    $time_span: TimeSpan!\n  ) {\n    getSpotlightViewCount(\n      first: $first\n      page: $page\n      time_span: $time_span\n    ) {\n      data {\n        id\n        title\n        body\n        count\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetSpotlightViewCount(\n    $first: Int!\n    $page: Int\n    $time_span: TimeSpan!\n  ) {\n    getSpotlightViewCount(\n      first: $first\n      page: $page\n      time_span: $time_span\n    ) {\n      data {\n        id\n        title\n        body\n        count\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDocumentSpotlightCount {\n    getDocumentSpotlightCount {\n      document_count\n      spotlight_count\n    }\n  }\n"): (typeof documents)["\n  query GetDocumentSpotlightCount {\n    getDocumentSpotlightCount {\n      document_count\n      spotlight_count\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDocumentViewCount(\n    $first: Int!\n    $page: Int\n    $time_span: TimeSpan!\n    $category_id: ID\n  ) {\n    getDocumentViewCount(\n      first: $first\n      page: $page\n      time_span: $time_span\n      category_id: $category_id\n    ) {\n      data {\n        document_id\n        document_title\n        view_count\n        child_category\n        parent_category\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetDocumentViewCount(\n    $first: Int!\n    $page: Int\n    $time_span: TimeSpan!\n    $category_id: ID\n  ) {\n    getDocumentViewCount(\n      first: $first\n      page: $page\n      time_span: $time_span\n      category_id: $category_id\n    ) {\n      data {\n        document_id\n        document_title\n        view_count\n        child_category\n        parent_category\n      }\n      paginator_info {\n        last_page\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCategoriesHome(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      parent_id\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCategoriesHome(\n    $keyword: String\n  ) {\n    getCategories(\n      keyword: $keyword\n    ) {\n      id\n      name\n      parent_id\n      is_leaf\n      is_classified\n      children {\n        id\n        name\n        is_leaf\n        is_classified\n        children {\n          id\n          name\n          is_leaf\n          is_classified\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetSpotlightById($id: ID!) {\n    getSpotlightById(id: $id) {\n      id\n    }\n  }\n"): (typeof documents)["\n  query GetSpotlightById($id: ID!) {\n    getSpotlightById(id: $id) {\n      id\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment InneihRecordFragment on InneihRecord {\n    id\n    inneih_registration_no: registration_no\n    mipa_hming\n    mipa_pa_hming\n    mipa_khua\n    hmeichhe_hming\n    hmeichhe_pa_hming\n    hmeichhe_khua\n    inneih_ni\n    hmun\n    inneihtirtu\n    register_book_number\n  }\n"): (typeof documents)["\n  fragment InneihRecordFragment on InneihRecord {\n    id\n    inneih_registration_no: registration_no\n    mipa_hming\n    mipa_pa_hming\n    mipa_khua\n    hmeichhe_hming\n    hmeichhe_pa_hming\n    hmeichhe_khua\n    inneih_ni\n    hmun\n    inneihtirtu\n    register_book_number\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  fragment BaptismaRecordFragment on BaptismaRecord {\n    id\n    baptisma_registration_no: registration_no\n    hming\n    pa_hming\n    nu_hming\n    pian_ni\n    pian_ni_remarks\n    baptisma_chan_ni\n    baptisma_chan_ni_remarks\n    khua\n    chantirtu\n    age\n    register_book_number\n  }\n"): (typeof documents)["\n  fragment BaptismaRecordFragment on BaptismaRecord {\n    id\n    baptisma_registration_no: registration_no\n    hming\n    pa_hming\n    nu_hming\n    pian_ni\n    pian_ni_remarks\n    baptisma_chan_ni\n    baptisma_chan_ni_remarks\n    khua\n    chantirtu\n    age\n    register_book_number\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDocumentsByCategoryId(\n    $first: Int!\n    $page: Int\n    $category_id: ID!\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocumentsByCategoryId(\n      first: $first\n      page: $page\n      category_id: $category_id\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      paginator_info {\n        last_page\n      }\n      data {\n        id\n        title\n        added_date\n        category {\n          id\n          name\n        }\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            ...InneihRecordFragment\n          }\n          ... on BaptismaRecord {\n            ...BaptismaRecordFragment\n          }\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetDocumentsByCategoryId(\n    $first: Int!\n    $page: Int\n    $category_id: ID!\n    $baptisma_filter: BaptismaRecordFilterInput\n    $inneih_filter: InneihRecordFilterInput\n    $others_filter: OtherRecordInput\n  ) {\n    getDocumentsByCategoryId(\n      first: $first\n      page: $page\n      category_id: $category_id\n      baptisma_filter: $baptisma_filter\n      inneih_filter: $inneih_filter\n      others_filter: $others_filter\n    ) {\n      paginator_info {\n        last_page\n      }\n      data {\n        id\n        title\n        added_date\n        category {\n          id\n          name\n        }\n        extra_record {\n          __typename\n          ... on InneihRecord {\n            ...InneihRecordFragment\n          }\n          ... on BaptismaRecord {\n            ...BaptismaRecordFragment\n          }\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetCategoryName(\n    $id: ID!\n  ) {\n    getCategoryById(\n      id: $id\n    ) {\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n"): (typeof documents)["\n  query GetCategoryName(\n    $id: ID!\n  ) {\n    getCategoryById(\n      id: $id\n    ) {\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  query GetDocumentById($id: ID!) {\n    getDocumentById(id: $id) {\n    id\n    title\n    body\n    tags\n    added_date\n    category {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n    extra_record {\n      __typename\n      ... on InneihRecord {\n        ...InneihRecordFragment\n      }\n      ... on BaptismaRecord {\n        ...BaptismaRecordFragment\n      }\n    }\n    files {\n      id\n      path\n      file_type\n    }\n\n    }\n  }\n"): (typeof documents)["\n  query GetDocumentById($id: ID!) {\n    getDocumentById(id: $id) {\n    id\n    title\n    body\n    tags\n    added_date\n    category {\n      id\n      name\n      parent {\n        id\n        name\n        parent {\n          id\n          name\n        }\n      }\n    }\n    extra_record {\n      __typename\n      ... on InneihRecord {\n        ...InneihRecordFragment\n      }\n      ... on BaptismaRecord {\n        ...BaptismaRecordFragment\n      }\n    }\n    files {\n      id\n      path\n      file_type\n    }\n\n    }\n  }\n"];
/**
 * The graphql function is used to parse GraphQL queries into a document that can be used by GraphQL clients.
 */
export function graphql(source: "\n  mutation Login(\n    $name: String!\n    $password: String!\n  ) {\n    login(name: $name, password: $password) {\n      token\n      exp_date\n    }\n  }\n"): (typeof documents)["\n  mutation Login(\n    $name: String!\n    $password: String!\n  ) {\n    login(name: $name, password: $password) {\n      token\n      exp_date\n    }\n  }\n"];

export function graphql(source: string) {
  return (documents as any)[source] ?? {};
}

export type DocumentType<TDocumentNode extends DocumentNode<any, any>> = TDocumentNode extends DocumentNode<  infer TType,  any>  ? TType  : never;